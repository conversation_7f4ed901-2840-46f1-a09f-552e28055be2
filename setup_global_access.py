#!/usr/bin/env python3
"""
🌍 SETUP GLOBAL ACCESS FOR JARVIS
Multiple methods to access <PERSON> from anywhere in the world
"""

import subprocess
import sys
import os
import time
import webbrowser

def setup_ngrok_auth():
    """Setup ngrok authentication to fix ERR_NGROK_8012."""
    print("🔧 SETTING UP NGROK AUTHENTICATION")
    print("=" * 40)
    print()
    
    print("📋 STEP-BY-STEP INSTRUCTIONS:")
    print("1. 🌐 Go to: https://ngrok.com/signup")
    print("2. 📝 Create a FREE account (30 seconds)")
    print("3. 🔑 Copy your authtoken from the dashboard")
    print("4. 🔧 Run the command below with your token")
    print()
    
    # Try to open ngrok signup page
    try:
        webbrowser.open("https://ngrok.com/signup")
        print("🌐 Opening ngrok signup page in browser...")
    except:
        print("💡 Manually go to: https://ngrok.com/signup")
    
    print()
    print("🔑 AFTER GETTING YOUR TOKEN:")
    print("Run this command (replace YOUR_TOKEN with actual token):")
    print("   ngrok config add-authtoken YOUR_TOKEN")
    print()
    
    # Check if ngrok is installed
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ ngrok is installed")
            print("📝 Example token format: 2abc123def456ghi789jkl")
            print()
            
            # Prompt for token
            token = input("🔑 Enter your ngrok authtoken (or press Enter to skip): ").strip()
            if token:
                try:
                    result = subprocess.run(['ngrok', 'config', 'add-authtoken', token], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        print("✅ ngrok authentication configured!")
                        print("🚀 You can now use global access")
                        return True
                    else:
                        print(f"❌ Error configuring ngrok: {result.stderr}")
                        return False
                except Exception as e:
                    print(f"❌ Error: {e}")
                    return False
            else:
                print("⏭️ Skipping ngrok setup")
                return False
        else:
            print("❌ ngrok not found")
            return False
    except:
        print("❌ ngrok not installed")
        print("💡 Download from: https://ngrok.com/download")
        return False

def setup_tailscale():
    """Setup Tailscale for global access."""
    print("\n🌐 TAILSCALE SETUP (RECOMMENDED)")
    print("=" * 35)
    print()
    
    print("✨ TAILSCALE BENEFITS:")
    print("• 🔒 Secure VPN connection")
    print("• 🌍 Access from anywhere")
    print("• 🚀 Fast and reliable")
    print("• 💰 Free for personal use")
    print("• 📱 Works on all devices")
    print()
    
    print("📋 SETUP INSTRUCTIONS:")
    print("1. 🌐 Go to: https://tailscale.com")
    print("2. 📝 Create account and download")
    print("3. 💻 Install on your computer")
    print("4. 📱 Install Tailscale app on iPhone")
    print("5. 🔗 Connect both devices")
    print("6. 🎯 Use Tailscale IP for Jarvis")
    print()
    
    # Try to open Tailscale website
    try:
        webbrowser.open("https://tailscale.com")
        print("🌐 Opening Tailscale website...")
    except:
        print("💡 Manually go to: https://tailscale.com")
    
    # Check if Tailscale is installed
    try:
        result = subprocess.run(['tailscale', 'status'], capture_output=True, text=True)
        if result.returncode == 0:
            print("\n✅ Tailscale is already installed!")
            print("📊 Current status:")
            print(result.stdout[:200] + "..." if len(result.stdout) > 200 else result.stdout)
            
            # Extract Tailscale IP
            lines = result.stdout.split('\n')
            for line in lines:
                if 'self' in line.lower():
                    parts = line.split()
                    if len(parts) > 0:
                        tailscale_ip = parts[0]
                        print(f"\n🎯 YOUR GLOBAL JARVIS URL:")
                        print(f"   http://{tailscale_ip}:8080")
                        print("📱 Use this URL on any device connected to Tailscale")
                        return True
        else:
            print("\n⚠️ Tailscale not connected")
            print("💡 Install and connect Tailscale first")
            return False
    except:
        print("\n❌ Tailscale not installed")
        print("💡 Download from: https://tailscale.com")
        return False

def setup_cloudflare_tunnel():
    """Setup Cloudflare Tunnel for global access."""
    print("\n☁️ CLOUDFLARE TUNNEL SETUP")
    print("=" * 30)
    print()
    
    print("✨ CLOUDFLARE TUNNEL BENEFITS:")
    print("• 🆓 Completely free")
    print("• 🔒 Secure HTTPS connection")
    print("• 🌍 Global access")
    print("• 🚀 Fast CDN network")
    print("• 📱 Works on all devices")
    print()
    
    print("📋 SETUP INSTRUCTIONS:")
    print("1. 🌐 Go to: https://dash.cloudflare.com")
    print("2. 📝 Create free account")
    print("3. 💻 Download cloudflared")
    print("4. 🔧 Run: cloudflared tunnel --url http://localhost:8080")
    print("5. 🎯 Get your public URL")
    print()
    
    # Try to open Cloudflare dashboard
    try:
        webbrowser.open("https://dash.cloudflare.com")
        print("🌐 Opening Cloudflare dashboard...")
    except:
        print("💡 Manually go to: https://dash.cloudflare.com")
    
    print("\n💡 QUICK START COMMAND:")
    print("cloudflared tunnel --url http://localhost:8080")
    print()

def test_global_access():
    """Test current global access setup."""
    print("\n🧪 TESTING GLOBAL ACCESS")
    print("=" * 25)
    print()
    
    # Test ngrok
    try:
        result = subprocess.run(['ngrok', 'config', 'check'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ ngrok: Authenticated and ready")
        else:
            print("❌ ngrok: Not authenticated")
    except:
        print("❌ ngrok: Not installed")
    
    # Test Tailscale
    try:
        result = subprocess.run(['tailscale', 'status'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Tailscale: Connected and ready")
        else:
            print("❌ Tailscale: Not connected")
    except:
        print("❌ Tailscale: Not installed")
    
    # Test Cloudflare
    try:
        result = subprocess.run(['cloudflared', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Cloudflare Tunnel: Installed")
        else:
            print("❌ Cloudflare Tunnel: Not installed")
    except:
        print("❌ Cloudflare Tunnel: Not installed")
    
    print()

def main():
    """Main setup function."""
    print("🌍 JARVIS GLOBAL ACCESS SETUP")
    print("=" * 35)
    print("🎯 Access Jarvis from anywhere in the world!")
    print()
    
    print("🔧 AVAILABLE METHODS:")
    print("1. 🚀 ngrok (Fix ERR_NGROK_8012)")
    print("2. 🌐 Tailscale (Recommended)")
    print("3. ☁️ Cloudflare Tunnel (Free)")
    print("4. 🧪 Test current setup")
    print("5. ❌ Exit")
    print()
    
    while True:
        choice = input("🔢 Choose option (1-5): ").strip()
        
        if choice == '1':
            if setup_ngrok_auth():
                print("\n🎉 ngrok setup complete!")
                print("🚀 Run: python jarvis_complete_launcher.py")
                break
            else:
                print("\n⚠️ ngrok setup failed, try another method")
                
        elif choice == '2':
            if setup_tailscale():
                print("\n🎉 Tailscale ready!")
                print("🚀 Run: python jarvis_complete_launcher.py")
                break
            else:
                print("\n⚠️ Tailscale setup needed")
                
        elif choice == '3':
            setup_cloudflare_tunnel()
            print("\n💡 After setting up Cloudflare Tunnel:")
            print("🚀 Run: python jarvis_complete_launcher.py")
            break
            
        elif choice == '4':
            test_global_access()
            
        elif choice == '5':
            print("👋 Exiting setup")
            break
            
        else:
            print("❌ Invalid choice, try again")
    
    print("\n✅ Global access setup complete!")
    print("🌍 You can now access Jarvis from anywhere!")

if __name__ == "__main__":
    main()
