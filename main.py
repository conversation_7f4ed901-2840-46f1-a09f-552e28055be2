import uvicorn
import config
import subprocess
import time
import threading
from working_jarvis_gui import WorkingJarvisGUI as JarvisGUI
from ultra_conversation import UltraConversation

def start_jarvis():
    """Start Jarvis with the advanced GUI v7.0."""
    try:
        print("🚀✨ Launching J.A.R.V.I.S Advanced Interface v7.0...")
        print("🎨 Features: Futuristic design, Enhanced styling, Modern dark theme")
        print("🛡️ Advanced Security Manager with real functionality")
        print("🎓 Intelligent Knowledge Training with enhanced AI")
        print("📝 Smart File Creator with advanced templates")
        print("🌐 Web Intelligence Plus with enhanced learning")
        print("💬 Advanced Chat with confidence indicators")
        print("📊 Real-time monitoring and health tracking")
        print("✨ All functionality preserved with dramatically improved appearance!")

        # Create and run the advanced GUI
        gui = JarvisGUI()
        gui.run()
    except Exception as e:
        print(f"❌ Error starting Jarvis GUI: {e}")
        print("🔧 Please check your dependencies and try again.")
        import traceback
        traceback.print_exc()



def kill_existing_processes():
    """Kill any existing processes using our ports."""
    try:
        # Use netstat and taskkill method (more reliable on Windows)
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
        for line in result.stdout.split('\n'):
            if ':8010' in line and 'LISTENING' in line:
                pid = line.split()[-1]
                print(f"🔄 Killing existing process on port 8010 (PID: {pid})")
                subprocess.run(['taskkill', '/F', '/PID', pid], capture_output=True)
    except Exception as e:
        print(f"⚠️ Could not kill existing processes: {e}")

if __name__ == "__main__":
    llama_process = None
    try:
        # Kill any existing processes first
        kill_existing_processes()
        time.sleep(2)  # Wait for processes to fully terminate

        # Launch the Llama server as a separate process
        llama_command = [
            "python",
            "-m",
            "llama_cpp.server",
            "--model",
            config.LLAMA_MODEL_PATH,
            "--host",
            "0.0.0.0",
            "--port",
            "8010",
            "--n_ctx",
            "4096",
            "--n_gpu_layers",
            "35",
        ]
        print("Starting Llama server...")
        llama_process = subprocess.Popen(llama_command)

        # Optimized wait time for faster startup
        print("⚡ Waiting for optimized Llama server to initialize...")
        time.sleep(5)  # Reduced wait time for faster startup

        # Start the FastAPI server in a separate thread
        print("Starting FastAPI server...")
        fastapi_thread = threading.Thread(target=lambda: uvicorn.run("server:app", host=config.HOST, port=config.PORT, log_level="warning", access_log=False))
        fastapi_thread.daemon = True  # Allow the main thread to exit even if this thread is running
        fastapi_thread.start()

        # Start the Tkinter GUI
        print("Starting Jarvis GUI...")
        start_jarvis()

    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested by user")
        if llama_process is not None:
            llama_process.terminate()
    except Exception as e:
        print(f"❌ Critical error in main execution: {e}")
        import traceback
        traceback.print_exc()
        if llama_process is not None:
            llama_process.terminate()