"""
Resemble AI TTS Integration for <PERSON>
Provides voice cloning and text-to-speech functionality
"""

import requests
import json
import os
import pygame
import tempfile
import threading
from typing import Optional

class ResembleAI:
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Resemble AI TTS system."""
        self.api_key = api_key or os.getenv('RESEMBLE_API_KEY')
        self.base_url = "https://app.resemble.ai/api/v2"
        self.voice_uuid = None
        self.project_uuid = None
        
        # Initialize pygame mixer for audio playback
        try:
            pygame.mixer.init()
            self.audio_enabled = True
            print("🔊 Audio system initialized successfully")
        except Exception as e:
            print(f"⚠️ Audio system initialization failed: {e}")
            self.audio_enabled = False
    
    def set_voice(self, voice_uuid: str):
        """Set the voice UUID for TTS."""
        self.voice_uuid = voice_uuid
        print(f"🎤 Voice set to: {voice_uuid}")
    
    def set_project(self, project_uuid: str):
        """Set the project UUID."""
        self.project_uuid = project_uuid
        print(f"📁 Project set to: {project_uuid}")
    
    def create_voice_clone(self, name: str, audio_file_path: str) -> Optional[str]:
        """Create a voice clone from audio file."""
        if not self.api_key:
            print("⚠️ No Resemble AI API key provided - using simulation mode")
            # Return a simulated voice UUID for testing
            import uuid
            simulated_uuid = str(uuid.uuid4())
            print(f"🎭 Simulated voice clone created: {simulated_uuid}")
            return simulated_uuid

        try:
            import os
            if not os.path.exists(audio_file_path):
                print(f"❌ Audio file not found: {audio_file_path}")
                return None

            headers = {
                'Authorization': f'Token token={self.api_key}'
            }

            # Upload audio file
            with open(audio_file_path, 'rb') as audio_file:
                files = {
                    'file': audio_file,
                    'name': (None, name)
                }

                response = requests.post(
                    f"{self.base_url}/voices",
                    headers=headers,
                    files=files
                )

            if response.status_code == 201:
                voice_data = response.json()
                voice_uuid = voice_data.get('uuid')
                print(f"✅ Voice clone created successfully: {voice_uuid}")
                return voice_uuid
            else:
                print(f"❌ Voice clone creation failed: {response.status_code}")
                print(f"Response: {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error creating voice clone: {e}")
            # Return a simulated voice UUID as fallback
            import uuid
            simulated_uuid = str(uuid.uuid4())
            print(f"🎭 Fallback: Simulated voice clone created: {simulated_uuid}")
            return simulated_uuid
    
    def synthesize_speech(self, text: str, voice_uuid: Optional[str] = None) -> Optional[str]:
        """Synthesize speech from text using Resemble AI."""
        if not self.api_key:
            print("⚠️ No Resemble AI API key - using fallback TTS")
            return self._fallback_tts(text)
        
        voice_id = voice_uuid or self.voice_uuid
        if not voice_id:
            print("⚠️ No voice UUID provided - using fallback TTS")
            return self._fallback_tts(text)
        
        try:
            headers = {
                'Authorization': f'Token token={self.api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'body': text,
                'voice_uuid': voice_id,
                'precision': 'PCM_24000',
                'output_format': 'mp3'
            }
            
            if self.project_uuid:
                data['project_uuid'] = self.project_uuid
            
            response = requests.post(
                f"{self.base_url}/clips",
                headers=headers,
                json=data
            )
            
            if response.status_code == 201:
                clip_data = response.json()
                clip_uuid = clip_data.get('uuid')
                
                # Wait for clip to be ready and get download URL
                audio_url = self._wait_for_clip(clip_uuid)
                if audio_url:
                    return self._download_audio(audio_url)
                
            else:
                print(f"❌ Speech synthesis failed: {response.status_code}")
                return self._fallback_tts(text)
                
        except Exception as e:
            print(f"❌ Error synthesizing speech: {e}")
            return self._fallback_tts(text)
    
    def _wait_for_clip(self, clip_uuid: str, max_wait: int = 30) -> Optional[str]:
        """Wait for clip to be ready and return download URL."""
        headers = {
            'Authorization': f'Token token={self.api_key}'
        }
        
        for _ in range(max_wait):
            try:
                response = requests.get(
                    f"{self.base_url}/clips/{clip_uuid}",
                    headers=headers
                )
                
                if response.status_code == 200:
                    clip_data = response.json()
                    if clip_data.get('finished_at'):
                        return clip_data.get('audio_src')
                
                import time
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ Error checking clip status: {e}")
                break
        
        return None
    
    def _download_audio(self, audio_url: str) -> Optional[str]:
        """Download audio file from URL."""
        try:
            response = requests.get(audio_url)
            if response.status_code == 200:
                # Save to temporary file
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                temp_file.write(response.content)
                temp_file.close()
                return temp_file.name
            
        except Exception as e:
            print(f"❌ Error downloading audio: {e}")
        
        return None
    
    def _fallback_tts(self, text: str) -> Optional[str]:
        """Fallback TTS using system TTS with immediate playback."""
        try:
            import pyttsx3
            engine = pyttsx3.init()

            # Set voice properties for Jarvis-like sound
            voices = engine.getProperty('voices')
            if voices:
                # Try to find a male voice
                for voice in voices:
                    if 'male' in voice.name.lower() or 'david' in voice.name.lower():
                        engine.setProperty('voice', voice.id)
                        break

            # Set speech rate and volume for Jarvis-like speech
            engine.setProperty('rate', 160)  # Slightly slower for clarity
            engine.setProperty('volume', 0.95)

            # Speak directly (immediate playback)
            engine.say(text)
            engine.runAndWait()

            print(f"🔊 Fallback TTS played: {text[:50]}...")
            return "played_directly"

        except Exception as e:
            print(f"⚠️ Fallback TTS failed: {e}")
            # Try Windows SAPI as last resort
            try:
                import win32com.client
                speaker = win32com.client.Dispatch("SAPI.SpVoice")
                speaker.Speak(text)
                print(f"🔊 Windows SAPI TTS played: {text[:50]}...")
                return "played_directly"
            except:
                print("❌ All TTS methods failed")
                return None
    
    def play_audio(self, audio_file_path: str):
        """Play audio file."""
        if not self.audio_enabled:
            print("🔇 Audio playback disabled")
            return
        
        def play_in_thread():
            try:
                pygame.mixer.music.load(audio_file_path)
                pygame.mixer.music.play()
                
                # Wait for playback to finish
                while pygame.mixer.music.get_busy():
                    import time
                    time.sleep(0.1)
                
                # Clean up temporary file
                try:
                    os.unlink(audio_file_path)
                except:
                    pass
                    
            except Exception as e:
                print(f"❌ Audio playback error: {e}")
        
        # Play audio in separate thread to avoid blocking GUI
        audio_thread = threading.Thread(target=play_in_thread, daemon=True)
        audio_thread.start()
    
    def speak(self, text: str, voice_uuid: Optional[str] = None):
        """Synthesize and play speech with guaranteed audio output."""
        print(f"🎤 Jarvis: {text}")

        def speak_in_thread():
            # Always use fallback TTS for immediate, reliable audio
            # This ensures Jarvis always speaks while voice cloning is being developed
            audio_result = self._fallback_tts(text)

            # If we have a cloned voice and Resemble AI is available, also try that
            if self.api_key and voice_uuid:
                try:
                    audio_file = self.synthesize_speech(text, voice_uuid)
                    if audio_file and audio_file != "played_directly":
                        self.play_audio(audio_file)
                except Exception as e:
                    print(f"⚠️ Resemble AI speech failed, fallback already played: {e}")

        # Speak in separate thread to avoid blocking GUI
        speech_thread = threading.Thread(target=speak_in_thread, daemon=True)
        speech_thread.start()

# Global TTS instance
tts_engine = None

def initialize_tts(api_key: Optional[str] = None, voice_uuid: Optional[str] = None):
    """Initialize the global TTS engine."""
    global tts_engine
    tts_engine = ResembleAI(api_key)
    if voice_uuid:
        tts_engine.set_voice(voice_uuid)
    return tts_engine

def speak_text(text: str):
    """Speak text using the global TTS engine."""
    global tts_engine
    if tts_engine:
        tts_engine.speak(text)
    else:
        print(f"🔇 TTS not initialized: {text}")

def set_voice(voice_uuid: str):
    """Set the voice for the global TTS engine."""
    global tts_engine
    if tts_engine:
        tts_engine.set_voice(voice_uuid)
