"""
Resemble AI TTS Integration for <PERSON>
Provides voice cloning and text-to-speech functionality
"""

import requests
import json
import os
import pygame
import tempfile
import threading
from typing import Optional

class ResembleAI:
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Resemble AI TTS system."""
        self.api_key = api_key or os.getenv('RESEMBLE_API_KEY')
        self.base_url = "https://app.resemble.ai/api/v2"
        self.voice_uuid = None
        self.project_uuid = None
        
        # Initialize pygame mixer for audio playback
        try:
            pygame.mixer.init()
            self.audio_enabled = True
            print("🔊 Audio system initialized successfully")
        except Exception as e:
            print(f"⚠️ Audio system initialization failed: {e}")
            self.audio_enabled = False
    
    def set_voice(self, voice_uuid: str):
        """Set the voice UUID for TTS."""
        self.voice_uuid = voice_uuid
        print(f"🎤 Voice set to: {voice_uuid}")
    
    def set_project(self, project_uuid: str):
        """Set the project UUID."""
        self.project_uuid = project_uuid
        print(f"📁 Project set to: {project_uuid}")
    
    def create_voice_clone(self, name: str, audio_file_path: str) -> Optional[str]:
        """Create a voice clone from audio file."""
        if not self.api_key:
            print("⚠️ No Resemble AI API key provided - using simulation mode")
            # Return a simulated voice UUID for testing
            import uuid
            simulated_uuid = str(uuid.uuid4())
            print(f"🎭 Simulated voice clone created: {simulated_uuid}")
            return simulated_uuid

        try:
            import os
            if not os.path.exists(audio_file_path):
                print(f"❌ Audio file not found: {audio_file_path}")
                return None

            headers = {
                'Authorization': f'Token token={self.api_key}'
            }

            # Upload audio file
            with open(audio_file_path, 'rb') as audio_file:
                files = {
                    'file': audio_file,
                    'name': (None, name)
                }

                response = requests.post(
                    f"{self.base_url}/voices",
                    headers=headers,
                    files=files
                )

            if response.status_code == 201:
                voice_data = response.json()
                voice_uuid = voice_data.get('uuid')
                print(f"✅ Voice clone created successfully: {voice_uuid}")
                return voice_uuid
            else:
                print(f"❌ Voice clone creation failed: {response.status_code}")
                print(f"Response: {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error creating voice clone: {e}")
            # Return a simulated voice UUID as fallback
            import uuid
            simulated_uuid = str(uuid.uuid4())
            print(f"🎭 Fallback: Simulated voice clone created: {simulated_uuid}")
            return simulated_uuid
    
    def synthesize_speech(self, text: str, voice_uuid: Optional[str] = None) -> Optional[str]:
        """Synthesize speech from text using Resemble AI."""
        if not self.api_key:
            print("⚠️ No Resemble AI API key - using fallback TTS")
            return self._fallback_tts(text)
        
        voice_id = voice_uuid or self.voice_uuid
        if not voice_id:
            print("⚠️ No voice UUID provided - using fallback TTS")
            return self._fallback_tts(text)
        
        try:
            headers = {
                'Authorization': f'Token token={self.api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'body': text,
                'voice_uuid': voice_id,
                'precision': 'PCM_24000',
                'output_format': 'mp3'
            }
            
            if self.project_uuid:
                data['project_uuid'] = self.project_uuid
            
            response = requests.post(
                f"{self.base_url}/clips",
                headers=headers,
                json=data
            )
            
            if response.status_code == 201:
                clip_data = response.json()
                clip_uuid = clip_data.get('uuid')
                
                # Wait for clip to be ready and get download URL
                audio_url = self._wait_for_clip(clip_uuid)
                if audio_url:
                    return self._download_audio(audio_url)
                
            else:
                print(f"❌ Speech synthesis failed: {response.status_code}")
                return self._fallback_tts(text)
                
        except Exception as e:
            print(f"❌ Error synthesizing speech: {e}")
            return self._fallback_tts(text)
    
    def _wait_for_clip(self, clip_uuid: str, max_wait: int = 30) -> Optional[str]:
        """Wait for clip to be ready and return download URL."""
        headers = {
            'Authorization': f'Token token={self.api_key}'
        }
        
        for _ in range(max_wait):
            try:
                response = requests.get(
                    f"{self.base_url}/clips/{clip_uuid}",
                    headers=headers
                )
                
                if response.status_code == 200:
                    clip_data = response.json()
                    if clip_data.get('finished_at'):
                        return clip_data.get('audio_src')
                
                import time
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ Error checking clip status: {e}")
                break
        
        return None
    
    def _download_audio(self, audio_url: str) -> Optional[str]:
        """Download audio file from URL."""
        try:
            response = requests.get(audio_url)
            if response.status_code == 200:
                # Save to temporary file
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                temp_file.write(response.content)
                temp_file.close()
                return temp_file.name
            
        except Exception as e:
            print(f"❌ Error downloading audio: {e}")
        
        return None
    
    def _fallback_tts(self, text: str) -> Optional[str]:
        """Fallback TTS using system TTS with voice customization and thread safety."""
        try:
            # Use a lock to prevent multiple TTS engines from running simultaneously
            import threading
            if not hasattr(self, '_tts_lock'):
                self._tts_lock = threading.Lock()

            with self._tts_lock:
                import pyttsx3

                # Create a new engine instance for thread safety
                try:
                    engine = pyttsx3.init(driverName='sapi5')
                except:
                    try:
                        engine = pyttsx3.init()
                    except:
                        print("⚠️ pyttsx3 initialization failed")
                        return self._windows_sapi_fallback(text)

                # Get available voices
                voices = engine.getProperty('voices')

                # Apply voice customization based on current voice_uuid
                if self.voice_uuid and voices:
                    # Use voice_uuid to determine voice characteristics
                    voice_hash = hash(self.voice_uuid) % len(voices)
                    selected_voice = voices[voice_hash]
                    engine.setProperty('voice', selected_voice.id)
                    print(f"🎤 Using voice: {selected_voice.name} (based on {self.voice_uuid[:8]}...)")
                elif voices:
                    # Default: Try to find a male voice for Jarvis
                    for voice in voices:
                        if 'male' in voice.name.lower() or 'david' in voice.name.lower():
                            engine.setProperty('voice', voice.id)
                            print(f"🎤 Using default voice: {voice.name}")
                            break

                # Customize speech parameters based on voice_uuid
                if self.voice_uuid:
                    # Use voice_uuid to create unique speech characteristics
                    voice_num = hash(self.voice_uuid) % 100
                    rate = 140 + (voice_num % 40)  # Rate between 140-180
                    volume = 0.85 + (voice_num % 15) / 100  # Volume between 0.85-1.0
                else:
                    # Default Jarvis settings
                    rate = 160
                    volume = 0.95

                engine.setProperty('rate', rate)
                engine.setProperty('volume', volume)

                # Speak directly (immediate playback)
                engine.say(text)
                engine.runAndWait()

                # Clean up engine
                try:
                    engine.stop()
                except:
                    pass

                print(f"🔊 Fallback TTS played: {text[:50]}... (Rate: {rate}, Volume: {volume:.2f})")
                return "played_directly"

        except Exception as e:
            print(f"⚠️ Fallback TTS failed: {e}")
            return self._windows_sapi_fallback(text)

    def _windows_sapi_fallback(self, text: str) -> Optional[str]:
        """Windows SAPI fallback with thread safety."""
        try:
            import win32com.client
            import pythoncom

            # Initialize COM for this thread
            pythoncom.CoInitialize()

            try:
                speaker = win32com.client.Dispatch("SAPI.SpVoice")

                # Try to customize Windows SAPI voice too
                if self.voice_uuid:
                    try:
                        voices = speaker.GetVoices()
                        if voices.Count > 0:
                            voice_index = hash(self.voice_uuid) % voices.Count
                            speaker.Voice = voices.Item(voice_index)
                            print(f"🎤 Windows SAPI using voice index: {voice_index}")
                    except:
                        pass  # Use default voice if customization fails

                speaker.Speak(text)
                print(f"🔊 Windows SAPI TTS played: {text[:50]}...")
                return "played_directly"

            finally:
                # Clean up COM
                pythoncom.CoUninitialize()

        except Exception as sapi_error:
            print(f"❌ Windows SAPI also failed: {sapi_error}")
            print("❌ All TTS methods failed")
            return None
    
    def play_audio(self, audio_file_path: str):
        """Play audio file."""
        if not self.audio_enabled:
            print("🔇 Audio playback disabled")
            return
        
        def play_in_thread():
            try:
                pygame.mixer.music.load(audio_file_path)
                pygame.mixer.music.play()
                
                # Wait for playback to finish
                while pygame.mixer.music.get_busy():
                    import time
                    time.sleep(0.1)
                
                # Clean up temporary file
                try:
                    os.unlink(audio_file_path)
                except:
                    pass
                    
            except Exception as e:
                print(f"❌ Audio playback error: {e}")
        
        # Play audio in separate thread to avoid blocking GUI
        audio_thread = threading.Thread(target=play_in_thread, daemon=True)
        audio_thread.start()
    
    def speak(self, text: str, voice_uuid: Optional[str] = None):
        """Synthesize and play speech with guaranteed audio output and better thread safety."""
        print(f"🎤 Jarvis: {text}")

        # Use a global lock to prevent multiple TTS calls
        if not hasattr(self, '_speak_lock'):
            self._speak_lock = threading.Lock()

        def speak_in_thread():
            # Wait for lock with shorter timeout and better handling
            if self._speak_lock.acquire(timeout=2):
                try:
                    # Use fallback TTS for immediate, reliable audio
                    self._fallback_tts(text)

                finally:
                    self._speak_lock.release()
            else:
                # If timeout, try without lock (emergency fallback)
                print(f"🔇 TTS busy, using emergency fallback: {text[:30]}...")
                try:
                    self._fallback_tts(text)
                except Exception as e:
                    print(f"⚠️ Emergency TTS failed: {e}")

        # Speak in separate thread to avoid blocking GUI
        speech_thread = threading.Thread(target=speak_in_thread, daemon=True)
        speech_thread.start()

# Global TTS instance
tts_engine = None

def initialize_tts(api_key: Optional[str] = None, voice_uuid: Optional[str] = None):
    """Initialize the global TTS engine."""
    global tts_engine
    tts_engine = ResembleAI(api_key)
    if voice_uuid:
        tts_engine.set_voice(voice_uuid)
    return tts_engine

def speak_text(text: str):
    """Speak text using the global TTS engine."""
    global tts_engine
    if tts_engine:
        tts_engine.speak(text)
    else:
        print(f"🔇 TTS not initialized: {text}")

def set_voice(voice_uuid: str):
    """Set the voice for the global TTS engine."""
    global tts_engine
    if tts_engine:
        tts_engine.set_voice(voice_uuid)
