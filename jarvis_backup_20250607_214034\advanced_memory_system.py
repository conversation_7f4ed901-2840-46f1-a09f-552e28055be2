"""
Advanced Memory System for Jarvis
=================================

This module implements a sophisticated memory system that allows <PERSON> to:
- Remember long-term conversations and context
- Build user profiles and preferences
- Track emotional states and communication patterns
- Maintain contextual awareness across sessions
- Learn and adapt from all interactions

Features:
- Long-term episodic memory
- Semantic knowledge networks
- User profiling and preference learning
- Emotional intelligence and mood tracking
- Contextual conversation threading
- Memory consolidation and retrieval
- Adaptive learning from interactions

Author: Jarvis AI System
Version: 1.0 (Advanced Memory)
"""

import sqlite3
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
import re
import hashlib

@dataclass
class MemoryEntry:
    """Individual memory entry."""
    id: str
    timestamp: str
    memory_type: str  # episodic, semantic, procedural, emotional
    content: str
    context: Dict[str, Any]
    importance: float  # 0.0 to 1.0
    emotional_valence: float  # -1.0 to 1.0 (negative to positive)
    tags: List[str]
    related_memories: List[str]
    access_count: int = 0
    last_accessed: str = ""

@dataclass
class UserProfile:
    """User profile and preferences."""
    user_id: str
    name: str
    communication_style: str
    preferred_response_length: str
    topics_of_interest: List[str]
    emotional_patterns: Dict[str, float]
    interaction_history: Dict[str, int]
    preferences: Dict[str, Any]
    personality_traits: Dict[str, float]
    last_updated: str

class AdvancedMemorySystem:
    """Advanced memory system with long-term learning capabilities."""
    
    def __init__(self, gui_callback=None):
        self.gui_callback = gui_callback
        self.db_path = 'jarvis_advanced_memory.db'
        self.current_user = "default_user"
        
        # Memory configuration
        self.memory_config = {
            'max_episodic_memories': 10000,
            'max_semantic_memories': 5000,
            'consolidation_threshold': 0.7,
            'forgetting_curve_factor': 0.1,
            'importance_decay_rate': 0.05
        }
        
        # Initialize database
        self.init_memory_database()
        
        # Load user profile
        self.user_profile = self.load_user_profile(self.current_user)
        
        # Memory networks
        self.semantic_network = defaultdict(list)
        self.episodic_timeline = []
        
        print("🧠 Advanced Memory System initialized - Long-term learning active!")
    
    def init_memory_database(self):
        """Initialize the advanced memory database."""
        try:
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            cursor = self.conn.cursor()
            
            # Memory entries table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memory_entries (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    context TEXT,
                    importance REAL DEFAULT 0.5,
                    emotional_valence REAL DEFAULT 0.0,
                    tags TEXT,
                    related_memories TEXT,
                    access_count INTEGER DEFAULT 0,
                    last_accessed TEXT,
                    user_id TEXT DEFAULT 'default_user'
                )
            ''')
            
            # User profiles table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    name TEXT,
                    communication_style TEXT DEFAULT 'professional',
                    preferred_response_length TEXT DEFAULT 'medium',
                    topics_of_interest TEXT,
                    emotional_patterns TEXT,
                    interaction_history TEXT,
                    preferences TEXT,
                    personality_traits TEXT,
                    last_updated TEXT
                )
            ''')
            
            # Conversation threads table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_threads (
                    thread_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    topic TEXT,
                    start_time TEXT,
                    last_activity TEXT,
                    message_count INTEGER DEFAULT 0,
                    emotional_tone REAL DEFAULT 0.0,
                    importance REAL DEFAULT 0.5,
                    status TEXT DEFAULT 'active'
                )
            ''')
            
            # Memory associations table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memory_associations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    memory_id_1 TEXT,
                    memory_id_2 TEXT,
                    association_strength REAL DEFAULT 0.5,
                    association_type TEXT,
                    created_at TEXT,
                    FOREIGN KEY (memory_id_1) REFERENCES memory_entries (id),
                    FOREIGN KEY (memory_id_2) REFERENCES memory_entries (id)
                )
            ''')
            
            self.conn.commit()
            print("🧠 Advanced memory database initialized successfully!")
            
        except Exception as e:
            print(f"❌ Memory database initialization error: {e}")
            self.conn = None
    
    def store_memory(self, content: str, memory_type: str = "episodic", 
                    context: Dict[str, Any] = None, importance: float = 0.5,
                    emotional_valence: float = 0.0, tags: List[str] = None) -> str:
        """Store a new memory entry."""
        try:
            # Generate unique memory ID
            memory_id = hashlib.md5(f"{content}{time.time()}".encode()).hexdigest()
            
            # Create memory entry
            memory = MemoryEntry(
                id=memory_id,
                timestamp=datetime.now().isoformat(),
                memory_type=memory_type,
                content=content,
                context=context or {},
                importance=importance,
                emotional_valence=emotional_valence,
                tags=tags or [],
                related_memories=[],
                access_count=0,
                last_accessed=""
            )
            
            # Store in database
            if self.conn:
                cursor = self.conn.cursor()
                cursor.execute('''
                    INSERT INTO memory_entries 
                    (id, timestamp, memory_type, content, context, importance, 
                     emotional_valence, tags, related_memories, user_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    memory.id, memory.timestamp, memory.memory_type, memory.content,
                    json.dumps(memory.context), memory.importance, memory.emotional_valence,
                    json.dumps(memory.tags), json.dumps(memory.related_memories),
                    self.current_user
                ))
                self.conn.commit()
            
            # Find and create associations
            self.create_memory_associations(memory)
            
            # Update user profile based on memory
            self.update_user_profile_from_memory(memory)
            
            print(f"🧠 Memory stored: {memory_type} - {content[:50]}...")
            return memory_id
            
        except Exception as e:
            print(f"❌ Error storing memory: {e}")
            return ""
    
    def retrieve_memories(self, query: str, memory_type: str = None, 
                         limit: int = 10, min_relevance: float = 0.3) -> List[MemoryEntry]:
        """Retrieve relevant memories based on query."""
        try:
            if not self.conn:
                return []
            
            cursor = self.conn.cursor()
            
            # Build query
            sql_query = '''
                SELECT * FROM memory_entries 
                WHERE user_id = ? AND (
                    content LIKE ? OR 
                    tags LIKE ? OR
                    context LIKE ?
                )
            '''
            params = [self.current_user, f'%{query}%', f'%{query}%', f'%{query}%']
            
            if memory_type:
                sql_query += ' AND memory_type = ?'
                params.append(memory_type)
            
            sql_query += ' ORDER BY importance DESC, timestamp DESC LIMIT ?'
            params.append(limit)
            
            cursor.execute(sql_query, params)
            rows = cursor.fetchall()
            
            memories = []
            for row in rows:
                memory = MemoryEntry(
                    id=row[0], timestamp=row[1], memory_type=row[2], content=row[3],
                    context=json.loads(row[4] or '{}'), importance=row[5],
                    emotional_valence=row[6], tags=json.loads(row[7] or '[]'),
                    related_memories=json.loads(row[8] or '[]'),
                    access_count=row[9], last_accessed=row[10] or ""
                )
                
                # Calculate relevance score
                relevance = self.calculate_relevance(query, memory)
                if relevance >= min_relevance:
                    memories.append(memory)
                    # Update access count
                    self.update_memory_access(memory.id)
            
            return sorted(memories, key=lambda m: m.importance, reverse=True)
            
        except Exception as e:
            print(f"❌ Error retrieving memories: {e}")
            return []
    
    def calculate_relevance(self, query: str, memory: MemoryEntry) -> float:
        """Calculate relevance score between query and memory."""
        try:
            query_lower = query.lower()
            content_lower = memory.content.lower()
            
            # Direct content match
            content_score = 0.0
            if query_lower in content_lower:
                content_score = 0.8
            else:
                # Word overlap
                query_words = set(query_lower.split())
                content_words = set(content_lower.split())
                overlap = len(query_words.intersection(content_words))
                content_score = overlap / max(len(query_words), 1) * 0.6
            
            # Tag match
            tag_score = 0.0
            for tag in memory.tags:
                if query_lower in tag.lower():
                    tag_score = 0.7
                    break
            
            # Context match
            context_score = 0.0
            context_str = json.dumps(memory.context).lower()
            if query_lower in context_str:
                context_score = 0.5
            
            # Combine scores
            relevance = max(content_score, tag_score, context_score)
            
            # Boost by importance and recency
            importance_boost = memory.importance * 0.2
            
            # Recency boost (memories from last 24 hours get boost)
            try:
                memory_time = datetime.fromisoformat(memory.timestamp)
                hours_ago = (datetime.now() - memory_time).total_seconds() / 3600
                recency_boost = max(0, (24 - hours_ago) / 24) * 0.1
            except:
                recency_boost = 0
            
            return min(relevance + importance_boost + recency_boost, 1.0)
            
        except Exception as e:
            print(f"❌ Error calculating relevance: {e}")
            return 0.0
    
    def create_memory_associations(self, memory: MemoryEntry):
        """Create associations between memories."""
        try:
            if not self.conn:
                return
            
            # Find similar memories
            similar_memories = self.retrieve_memories(
                memory.content[:100], limit=5, min_relevance=0.4
            )
            
            cursor = self.conn.cursor()
            
            for similar_memory in similar_memories:
                if similar_memory.id != memory.id:
                    # Calculate association strength
                    strength = self.calculate_association_strength(memory, similar_memory)
                    
                    if strength > 0.3:
                        # Store association
                        cursor.execute('''
                            INSERT OR REPLACE INTO memory_associations
                            (memory_id_1, memory_id_2, association_strength, 
                             association_type, created_at)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (
                            memory.id, similar_memory.id, strength,
                            'semantic', datetime.now().isoformat()
                        ))
            
            self.conn.commit()
            
        except Exception as e:
            print(f"❌ Error creating associations: {e}")
    
    def calculate_association_strength(self, memory1: MemoryEntry, memory2: MemoryEntry) -> float:
        """Calculate association strength between two memories."""
        try:
            # Content similarity
            content_sim = self.calculate_relevance(memory1.content, memory2)
            
            # Tag overlap
            tags1 = set(memory1.tags)
            tags2 = set(memory2.tags)
            tag_overlap = len(tags1.intersection(tags2)) / max(len(tags1.union(tags2)), 1)
            
            # Temporal proximity
            try:
                time1 = datetime.fromisoformat(memory1.timestamp)
                time2 = datetime.fromisoformat(memory2.timestamp)
                time_diff = abs((time1 - time2).total_seconds())
                temporal_sim = max(0, 1 - (time_diff / (24 * 3600)))  # 24 hour window
            except:
                temporal_sim = 0
            
            # Emotional similarity
            emotional_sim = 1 - abs(memory1.emotional_valence - memory2.emotional_valence) / 2
            
            # Combine factors
            strength = (content_sim * 0.4 + tag_overlap * 0.3 + 
                       temporal_sim * 0.2 + emotional_sim * 0.1)
            
            return min(strength, 1.0)
            
        except Exception as e:
            print(f"❌ Error calculating association strength: {e}")
            return 0.0
    
    def update_memory_access(self, memory_id: str):
        """Update memory access statistics."""
        try:
            if self.conn:
                cursor = self.conn.cursor()
                cursor.execute('''
                    UPDATE memory_entries 
                    SET access_count = access_count + 1, last_accessed = ?
                    WHERE id = ?
                ''', (datetime.now().isoformat(), memory_id))
                self.conn.commit()
        except Exception as e:
            print(f"❌ Error updating memory access: {e}")
    
    def load_user_profile(self, user_id: str) -> UserProfile:
        """Load or create user profile."""
        try:
            if self.conn:
                cursor = self.conn.cursor()
                cursor.execute('SELECT * FROM user_profiles WHERE user_id = ?', (user_id,))
                row = cursor.fetchone()
                
                if row:
                    return UserProfile(
                        user_id=row[0], name=row[1] or "User",
                        communication_style=row[2], preferred_response_length=row[3],
                        topics_of_interest=json.loads(row[4] or '[]'),
                        emotional_patterns=json.loads(row[5] or '{}'),
                        interaction_history=json.loads(row[6] or '{}'),
                        preferences=json.loads(row[7] or '{}'),
                        personality_traits=json.loads(row[8] or '{}'),
                        last_updated=row[9] or datetime.now().isoformat()
                    )
            
            # Create new profile
            profile = UserProfile(
                user_id=user_id, name="User", communication_style="professional",
                preferred_response_length="medium", topics_of_interest=[],
                emotional_patterns={}, interaction_history={},
                preferences={}, personality_traits={},
                last_updated=datetime.now().isoformat()
            )
            
            self.save_user_profile(profile)
            return profile
            
        except Exception as e:
            print(f"❌ Error loading user profile: {e}")
            return UserProfile(
                user_id=user_id, name="User", communication_style="professional",
                preferred_response_length="medium", topics_of_interest=[],
                emotional_patterns={}, interaction_history={},
                preferences={}, personality_traits={},
                last_updated=datetime.now().isoformat()
            )
    
    def save_user_profile(self, profile: UserProfile):
        """Save user profile to database."""
        try:
            if self.conn:
                cursor = self.conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO user_profiles
                    (user_id, name, communication_style, preferred_response_length,
                     topics_of_interest, emotional_patterns, interaction_history,
                     preferences, personality_traits, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    profile.user_id, profile.name, profile.communication_style,
                    profile.preferred_response_length, json.dumps(profile.topics_of_interest),
                    json.dumps(profile.emotional_patterns), json.dumps(profile.interaction_history),
                    json.dumps(profile.preferences), json.dumps(profile.personality_traits),
                    profile.last_updated
                ))
                self.conn.commit()
        except Exception as e:
            print(f"❌ Error saving user profile: {e}")
    
    def update_user_profile_from_memory(self, memory: MemoryEntry):
        """Update user profile based on new memory."""
        try:
            # Extract topics from memory
            words = memory.content.lower().split()
            topics = [word for word in words if len(word) > 4 and word.isalpha()]
            
            # Update topics of interest
            for topic in topics[:3]:  # Top 3 topics
                if topic not in self.user_profile.topics_of_interest:
                    self.user_profile.topics_of_interest.append(topic)
            
            # Keep only top 20 topics
            self.user_profile.topics_of_interest = self.user_profile.topics_of_interest[-20:]
            
            # Update emotional patterns
            emotion_key = memory.memory_type
            if emotion_key not in self.user_profile.emotional_patterns:
                self.user_profile.emotional_patterns[emotion_key] = 0.0
            
            # Moving average of emotional valence
            current_avg = self.user_profile.emotional_patterns[emotion_key]
            self.user_profile.emotional_patterns[emotion_key] = (
                current_avg * 0.9 + memory.emotional_valence * 0.1
            )
            
            # Update interaction history
            today = datetime.now().strftime('%Y-%m-%d')
            if today not in self.user_profile.interaction_history:
                self.user_profile.interaction_history[today] = 0
            self.user_profile.interaction_history[today] += 1
            
            # Update timestamp
            self.user_profile.last_updated = datetime.now().isoformat()
            
            # Save profile
            self.save_user_profile(self.user_profile)
            
        except Exception as e:
            print(f"❌ Error updating user profile: {e}")
    
    def get_contextual_memories(self, current_input: str, limit: int = 5) -> List[MemoryEntry]:
        """Get contextually relevant memories for current input."""
        try:
            # Get recent conversation context
            recent_memories = self.retrieve_memories(
                current_input, memory_type="episodic", limit=limit
            )
            
            # Get semantic knowledge
            semantic_memories = self.retrieve_memories(
                current_input, memory_type="semantic", limit=limit//2
            )
            
            # Combine and sort by relevance
            all_memories = recent_memories + semantic_memories
            return sorted(all_memories, key=lambda m: m.importance, reverse=True)[:limit]
            
        except Exception as e:
            print(f"❌ Error getting contextual memories: {e}")
            return []
    
    def consolidate_memories(self):
        """Consolidate and optimize memory storage."""
        try:
            if not self.conn:
                return
            
            print("🧠 Starting memory consolidation...")
            
            # Remove low-importance, old memories
            cutoff_date = (datetime.now() - timedelta(days=30)).isoformat()
            
            cursor = self.conn.cursor()
            cursor.execute('''
                DELETE FROM memory_entries 
                WHERE importance < 0.3 AND timestamp < ? AND access_count < 2
            ''', (cutoff_date,))
            
            deleted_count = cursor.rowcount
            
            # Update importance scores based on access patterns
            cursor.execute('''
                UPDATE memory_entries 
                SET importance = importance * 0.95 + (access_count * 0.01)
                WHERE last_accessed < ?
            ''', (cutoff_date,))
            
            self.conn.commit()
            
            print(f"🧠 Memory consolidation complete - Removed {deleted_count} low-value memories")
            
        except Exception as e:
            print(f"❌ Error during memory consolidation: {e}")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory system statistics."""
        try:
            if not self.conn:
                return {}
            
            cursor = self.conn.cursor()
            
            # Total memories
            cursor.execute('SELECT COUNT(*) FROM memory_entries WHERE user_id = ?', (self.current_user,))
            total_memories = cursor.fetchone()[0]
            
            # Memories by type
            cursor.execute('''
                SELECT memory_type, COUNT(*) FROM memory_entries 
                WHERE user_id = ? GROUP BY memory_type
            ''', (self.current_user,))
            memory_types = dict(cursor.fetchall())
            
            # Recent activity
            week_ago = (datetime.now() - timedelta(days=7)).isoformat()
            cursor.execute('''
                SELECT COUNT(*) FROM memory_entries 
                WHERE user_id = ? AND timestamp > ?
            ''', (self.current_user, week_ago))
            recent_memories = cursor.fetchone()[0]
            
            return {
                'total_memories': total_memories,
                'memory_types': memory_types,
                'recent_memories': recent_memories,
                'user_profile': asdict(self.user_profile),
                'topics_of_interest': len(self.user_profile.topics_of_interest)
            }
            
        except Exception as e:
            print(f"❌ Error getting memory stats: {e}")
            return {}
