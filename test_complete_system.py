#!/usr/bin/env python3
"""
🧪 JARVIS COMPLETE SYSTEM TEST
Quick test to verify all components are working
"""

import os
import sys
import time
import subprocess
import requests
from datetime import datetime

def test_file_exists(filename):
    """Test if a file exists."""
    if os.path.exists(filename):
        print(f"✅ {filename} - EXISTS")
        return True
    else:
        print(f"❌ {filename} - MISSING")
        return False

def test_web_app_response():
    """Test if web app responds."""
    try:
        response = requests.get('http://127.0.0.1:8080', timeout=5)
        if response.status_code == 200:
            print("✅ Web app responding")
            return True
        else:
            print(f"⚠️ Web app status: {response.status_code}")
            return False
    except:
        print("❌ Web app not responding")
        return False

def test_api_endpoints():
    """Test API endpoints."""
    endpoints = [
        '/api/system/status',
        '/api/training/progress'
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f'http://127.0.0.1:8080{endpoint}', timeout=5)
            if response.status_code == 200:
                print(f"✅ API {endpoint} - OK")
            else:
                print(f"⚠️ API {endpoint} - Status {response.status_code}")
        except:
            print(f"❌ API {endpoint} - Failed")

def main():
    """Run complete system test."""
    print("🧪 JARVIS COMPLETE SYSTEM TEST")
    print("=" * 40)
    print(f"🕐 Test started: {datetime.now().strftime('%H:%M:%S')}")
    print()
    
    # Test 1: Check required files
    print("📁 TESTING FILE STRUCTURE")
    print("-" * 25)
    
    required_files = [
        'working_jarvis_gui.py',
        'jarvis_web_app.py', 
        'jarvis_complete_launcher.py',
        'jarvis_integration_bridge.py',
        'project_500_review.py',
        'resemble_tts.py',
        'enhanced_training_system.py',
        'templates/jarvis.html'
    ]
    
    files_ok = 0
    for file in required_files:
        if test_file_exists(file):
            files_ok += 1
    
    print(f"📊 Files: {files_ok}/{len(required_files)} OK")
    print()
    
    # Test 2: Check if web app is running
    print("🌐 TESTING WEB APP")
    print("-" * 18)
    
    if test_web_app_response():
        print("🎉 Web app is running!")
        
        # Test API endpoints
        print("\n🔌 TESTING API ENDPOINTS")
        print("-" * 23)
        test_api_endpoints()
    else:
        print("⚠️ Web app not running - start with launcher")
    
    print()
    
    # Test 3: System recommendations
    print("💡 SYSTEM RECOMMENDATIONS")
    print("-" * 26)
    
    if files_ok == len(required_files):
        print("✅ All files present - Ready to launch!")
        print("🚀 Run: python jarvis_complete_launcher.py")
    else:
        print("⚠️ Missing files - Check installation")
    
    print()
    print("📋 QUICK START GUIDE:")
    print("1. Run: python jarvis_complete_launcher.py")
    print("2. Desktop GUI will open automatically")
    print("3. Web app will be available at http://127.0.0.1:8080")
    print("4. Use voice or text on both interfaces")
    print("5. Say 'improve your functions' to test self-improvement")
    print("6. Say 'improve your automation function' for 500% automation")
    print()
    
    print("🎯 TEST COMMANDS TO TRY:")
    print("• 'Hello Jarvis' - Basic conversation")
    print("• 'train for 1 hour about AI' - Start training")
    print("• 'review your programming' - Self-analysis")
    print("• 'improve your functions' - Self-improvement")
    print("• 'improve your automation function' - 500% automation")
    print()
    
    print("✅ System test complete!")

if __name__ == "__main__":
    main()
