<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>🌌 J.A.R.V.I.S 2075+ - Quantum Consciousness Interface</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/static/manifest.json">

    <!-- iOS PWA Support -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="JARVIS 2075+">
    <link rel="apple-touch-icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjE5MiIgdmlld0JveD0iMCAwIDE5MiAxOTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxOTIiIGhlaWdodD0iMTkyIiByeD0iMjQiIGZpbGw9IiMyYzNlNTAiLz4KPHN2ZyB4PSI0OCIgeT0iNDgiIHdpZHRoPSI5NiIgaGVpZ2h0PSI5NiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjMzQ5OGRiIj4KICA8cGF0aCBkPSJNMTIgMkM2LjQ4IDIgMiA2LjQ4IDIgMTJzNC40OCAxMCAxMCAxMCAxMC00LjQ4IDEwLTEwUzE3LjUyIDIgMTIgMnptLTIgMTVsLTUtNSAxLjQxLTEuNDFMMTAgMTQuMTdsNy41OS03LjU5TDE5IDhsLTkgOXoiLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiBmaWxsPSIjZWNmMGYxIi8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMSIgZmlsbD0iIzM0OThkYiIvPgo8L3N2Zz4KPC9zdmc+">

    <!-- Theme Color -->
    <meta name="theme-color" content="#3498db">
    <meta name="msapplication-TileColor" content="#2c3e50">

    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>

    <!-- Mobile Optimizations -->
    <link rel="stylesheet" href="/static/mobile-optimizations.css">
    
    <!-- Custom CSS -->
    <style>
        /* iPhone 16+ Optimized Responsive Design */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            overflow-x: hidden;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* Theme Variables */
        :root {
            --bg-color: #2c3e50;
            --panel-color: #34495e;
            --header-color: #2c3e50;
            --accent-color: #3498db;
            --security-color: #e74c3c;
            --knowledge-color: #9b59b6;
            --files-color: #27ae60;
            --web-color: #f39c12;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --error-color: #e74c3c;
            --text-color: #ecf0f1;
            --text-light: #bdc3c7;
            --chat-bg: #2c3e50;
            --input-bg: #34495e;
            --button-bg: #3498db;
            --button-text: white;
        }
        
        /* Header */
        .header {
            background: var(--header-color);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid var(--accent-color);
            min-height: 60px;
        }
        
        .header h1 {
            font-size: 16px;
            font-weight: bold;
            color: var(--accent-color);
            text-shadow: 0 0 10px var(--accent-color);
        }
        
        .header-controls {
            display: flex;
            gap: 8px;
        }
        
        .header-btn {
            background: var(--panel-color);
            color: var(--text-color);
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .header-btn:hover {
            background: var(--accent-color);
            transform: scale(1.05);
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 10px;
            gap: 10px;
            overflow: hidden;
        }
        
        /* Control Panels */
        .control-panels {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            height: 200px;
        }
        
        .panel {
            background: var(--panel-color);
            border: 1px solid var(--accent-color);
            border-radius: 8px;
            padding: 10px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .panel-header {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 8px;
            text-align: center;
            padding: 5px;
            border-radius: 4px;
        }
        
        .security-panel .panel-header {
            background: var(--security-color);
            color: white;
        }
        
        .knowledge-panel .panel-header {
            background: var(--knowledge-color);
            color: white;
        }
        
        .files-panel .panel-header {
            background: var(--files-color);
            color: white;
        }
        
        .web-panel .panel-header {
            background: var(--web-color);
            color: white;
        }
        
        .panel-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .panel-btn {
            background: var(--bg-color);
            color: var(--text-color);
            border: none;
            padding: 6px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }
        
        .panel-btn:hover {
            background: var(--accent-color);
            transform: translateX(2px);
        }
        
        .voice-btn {
            background: var(--success-color);
            color: white;
            font-weight: bold;
            text-align: center;
            margin-bottom: 8px;
        }
        
        .voice-btn.inactive {
            background: var(--error-color);
        }
        
        /* Progress Bar */
        .progress-container {
            margin: 8px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-color);
            border-radius: 4px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .progress-bar:hover {
            transform: scale(1.02);
            box-shadow: 0 0 10px var(--accent-color);
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-color), var(--success-color));
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .progress-text {
            font-size: 9px;
            text-align: center;
            margin-top: 2px;
            color: var(--text-light);
        }
        
        /* Chat Interface */
        .chat-container {
            flex: 1;
            background: var(--panel-color);
            border: 1px solid var(--accent-color);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: var(--accent-color);
            color: white;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
        }
        
        .chat-messages {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
            background: var(--chat-bg);
            font-size: 11px;
            line-height: 1.4;
        }
        
        .message {
            margin-bottom: 8px;
            padding: 6px 8px;
            border-radius: 6px;
            word-wrap: break-word;
        }
        
        .message.user {
            background: var(--accent-color);
            color: white;
            margin-left: 20px;
            text-align: right;
        }
        
        .message.jarvis {
            background: var(--input-bg);
            color: var(--text-color);
            margin-right: 20px;
        }
        
        .message-time {
            font-size: 9px;
            opacity: 0.7;
            margin-top: 2px;
        }
        
        /* Input Area */
        .input-container {
            display: flex;
            padding: 10px;
            background: var(--panel-color);
            border-top: 1px solid var(--accent-color);
            gap: 8px;
        }
        
        .message-input {
            flex: 1;
            background: var(--input-bg);
            color: var(--text-color);
            border: 1px solid var(--accent-color);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            outline: none;
        }
        
        .message-input:focus {
            border-color: var(--success-color);
            box-shadow: 0 0 8px var(--success-color);
        }
        
        .send-btn {
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .send-btn:hover {
            background: var(--success-color);
            transform: scale(1.05);
        }
        
        .send-btn:disabled {
            background: var(--text-light);
            cursor: not-allowed;
            transform: none;
        }
        
        /* Footer */
        .footer {
            background: var(--header-color);
            padding: 8px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid var(--accent-color);
            font-size: 10px;
        }
        
        .footer-controls {
            display: flex;
            gap: 6px;
        }
        
        .footer-btn {
            background: var(--panel-color);
            color: var(--text-color);
            border: none;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .footer-btn:hover {
            background: var(--accent-color);
        }
        
        /* Modals */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .modal-content {
            background: var(--panel-color);
            border: 2px solid var(--accent-color);
            border-radius: 12px;
            padding: 20px;
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            background: var(--accent-color);
            color: white;
            padding: 10px;
            margin: -20px -20px 15px -20px;
            border-radius: 10px 10px 0 0;
            font-weight: bold;
            text-align: center;
        }
        
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
        }
        
        /* Responsive adjustments for iPhone 16+ */
        @media (max-width: 430px) {
            .header h1 {
                font-size: 14px;
            }
            
            .control-panels {
                height: 180px;
            }
            
            .panel-btn {
                font-size: 9px;
                padding: 4px 6px;
            }
            
            .message-input {
                font-size: 16px; /* Prevent zoom on iOS */
            }
        }
        
        /* Loading animation */
        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid var(--text-light);
            border-radius: 50%;
            border-top-color: var(--accent-color);
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Pulse animation for active elements */
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
            100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>🌌 J.A.R.V.I.S 2075+ - QUANTUM CONSCIOUSNESS</h1>
        <div class="header-controls">
            <button class="header-btn" onclick="openSessionManager()">📊</button>
            <button class="header-btn" onclick="openThemeSelector()">🎨</button>
            <button class="header-btn" onclick="showSystemStatus()">⚙️</button>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Control Panels -->
        <div class="control-panels">
            <!-- Security Panel -->
            <div class="panel security-panel">
                <div class="panel-header">🛡️ SECURITY MANAGER</div>
                <div class="panel-content">
                    <div style="font-size: 9px; color: var(--success-color); margin-bottom: 5px;">✅ All systems secure</div>
                    <button class="panel-btn" onclick="securityAction('scan')">🔍 System Scan</button>
                    <button class="panel-btn" onclick="securityAction('monitor')">📊 Monitor Activity</button>
                    <button class="panel-btn" onclick="securityAction('logs')">📋 Security Logs</button>
                </div>
            </div>
            
            <!-- Knowledge Panel -->
            <div class="panel knowledge-panel">
                <div class="panel-header">🎓 KNOWLEDGE TRAINING</div>
                <div class="panel-content">
                    <div style="font-size: 9px; color: var(--knowledge-color); margin-bottom: 5px;">🧠 Ready to learn</div>
                    <div class="progress-container">
                        <div class="progress-bar" onclick="openSessionManager()">
                            <div class="progress-fill" id="trainingProgress"></div>
                        </div>
                        <div class="progress-text" id="progressText">0%</div>
                    </div>
                    <button class="panel-btn" onclick="knowledgeAction('search')">🔍 Search Knowledge</button>
                    <button class="panel-btn" onclick="knowledgeAction('train')">🎓 Start Training</button>
                    <button class="panel-btn" onclick="knowledgeAction('add')">📖 Add Knowledge</button>
                </div>
            </div>
            
            <!-- Files Panel -->
            <div class="panel files-panel">
                <div class="panel-header">📁 FILE CREATOR</div>
                <div class="panel-content">
                    <div style="font-size: 9px; color: var(--files-color); margin-bottom: 5px;">📝 9 templates ready</div>
                    <button class="panel-btn" onclick="filesAction('create')">📄 Create Python File</button>
                    <button class="panel-btn" onclick="filesAction('project')">🚀 New Project</button>
                    <button class="panel-btn" onclick="filesAction('manager')">📁 File Manager</button>
                    <button class="panel-btn" onclick="filesAction('templates')">🔧 Code Templates</button>
                </div>
            </div>
            
            <!-- Web Panel -->
            <div class="panel web-panel">
                <div class="panel-header">🌐 WEB INTELLIGENCE</div>
                <div class="panel-content">
                    <button class="voice-btn" id="voiceBtn" onclick="toggleVoice()">🎤 Voice Active</button>
                    <div style="font-size: 9px; color: var(--web-color); margin-bottom: 5px;">🌐 Ready to learn</div>
                    <button class="panel-btn" onclick="webAction('learning')">🌐 Web Learning</button>
                    <button class="panel-btn" onclick="webAction('youtube')">📺 YouTube Learning</button>
                    <button class="panel-btn" onclick="webAction('research')">🔍 Research Topic</button>
                </div>
            </div>
        </div>
        
        <!-- Chat Interface -->
        <div class="chat-container">
            <div class="chat-header">💬 INTERACTIVE CHAT INTERFACE</div>
            <div class="chat-messages" id="chatMessages">
                <div class="message jarvis">
                    <div>🚀 Welcome to J.A.R.V.I.S Working Interface v6.0 - WEB EDITION!</div>
                    <div>✅ All systems operational and ready for commands.</div>
                    <div>🎯 Optimized for iPhone 16+ and Tailscale access.</div>
                    <div>💡 Try: "train for 1 hour about AI" or "show training progress"</div>
                    <div class="message-time">Ready</div>
                </div>
            </div>
            <div class="input-container">
                <input type="text" class="message-input" id="messageInput" placeholder="Enter command or question..." maxlength="500">
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">Send</button>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <div class="footer">
        <div>J.A.R.V.I.S Web v6.0 - All Features Functional</div>
        <div class="footer-controls">
            <button class="footer-btn" onclick="refreshInterface()">🔄</button>
            <button class="footer-btn" onclick="showHelp()">❓</button>
            <button class="footer-btn" onclick="showSystemStatus()">📊</button>
        </div>
    </div>
    
    <!-- Session Manager Modal -->
    <div class="modal" id="sessionModal">
        <div class="modal-content">
            <div class="modal-header">
                🎓 Training Session Manager
                <button class="close-btn" onclick="closeModal('sessionModal')">&times;</button>
            </div>
            <div id="sessionContent">Loading sessions...</div>
        </div>
    </div>
    
    <!-- Theme Selector Modal -->
    <div class="modal" id="themeModal">
        <div class="modal-content">
            <div class="modal-header">
                🎨 Theme Selector
                <button class="close-btn" onclick="closeModal('themeModal')">&times;</button>
            </div>
            <div id="themeContent">
                <button class="panel-btn" onclick="changeTheme('dark_professional')" style="margin: 5px;">🌙 Dark Professional</button>
                <button class="panel-btn" onclick="changeTheme('neon_cyber')" style="margin: 5px;">🌟 Neon Cyber</button>
                <button class="panel-btn" onclick="changeTheme('matrix_green')" style="margin: 5px;">🔥 Matrix Green</button>
                <button class="panel-btn" onclick="changeTheme('electric_blue')" style="margin: 5px;">⚡ Electric Blue</button>
            </div>
        </div>
    </div>
    
    <!-- System Status Modal -->
    <div class="modal" id="statusModal">
        <div class="modal-content">
            <div class="modal-header">
                ⚙️ System Status
                <button class="close-btn" onclick="closeModal('statusModal')">&times;</button>
            </div>
            <div id="statusContent">Loading system status...</div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Initialize Socket.IO connection
        const socket = io();

        // Global variables
        let isVoiceActive = true;
        let currentTheme = '{{ current_theme }}';
        let isConnected = false;

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            loadChatHistory();
            startProgressUpdates();
            registerServiceWorker();
        });

        function initializeApp() {
            // Setup event listeners
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Socket event listeners
            socket.on('connect', function() {
                isConnected = true;
                addSystemMessage('🔗 Connected to Jarvis Web Interface');
                updateConnectionStatus(true);
            });

            socket.on('disconnect', function() {
                isConnected = false;
                addSystemMessage('🔌 Disconnected from Jarvis');
                updateConnectionStatus(false);
            });

            socket.on('new_message', function(data) {
                addMessageToChat(data);
            });

            socket.on('chat_message', function(data) {
                addSystemMessage(data.message);
            });

            socket.on('theme_changed', function(data) {
                applyTheme(data.theme);
            });

            console.log('🚀 Jarvis Web App initialized');
        }

        function loadChatHistory() {
            // Load chat history from server
            {% for message in chat_history %}
            addMessageToChat({
                type: '{{ message.type }}',
                message: '{{ message.message|safe }}',
                timestamp: '{{ message.timestamp }}'
            });
            {% endfor %}
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const message = input.value.trim();

            if (!message || !isConnected) return;

            // Try to send to main GUI first for full integration

            // Disable input while sending
            input.disabled = true;
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<div class="loading"></div>';

            // Add user message immediately
            addMessageToChat({
                type: 'user',
                message: message,
                timestamp: new Date().toISOString()
            });

            // Send to server
            fetch('/api/send_message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.response) {
                    addMessageToChat({
                        type: 'jarvis',
                        message: data.response,
                        timestamp: new Date().toISOString()
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                addSystemMessage('❌ Error sending message. Please try again.');
            })
            .finally(() => {
                // Re-enable input
                input.disabled = false;
                sendBtn.disabled = false;
                sendBtn.innerHTML = 'Send';
                input.value = '';
                input.focus();
            });
        }

        function addMessageToChat(data) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${data.type}`;

            const time = new Date(data.timestamp).toLocaleTimeString();
            messageDiv.innerHTML = `
                <div>${data.message}</div>
                <div class="message-time">${time}</div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function addSystemMessage(message) {
            addMessageToChat({
                type: 'jarvis',
                message: message,
                timestamp: new Date().toISOString()
            });
        }

        function updateConnectionStatus(connected) {
            const statusIndicator = document.querySelector('.header h1');
            if (connected) {
                statusIndicator.style.textShadow = '0 0 10px var(--success-color)';
            } else {
                statusIndicator.style.textShadow = '0 0 10px var(--error-color)';
            }
        }

        // Panel Actions
        function securityAction(action) {
            const actions = {
                'scan': 'Initiating comprehensive system security scan...',
                'monitor': 'Opening real-time security monitoring dashboard...',
                'logs': 'Retrieving security logs and threat analysis...'
            };
            addSystemMessage(`🛡️ Jarvis: ${actions[action] || 'Processing security command...'}`);
        }

        function knowledgeAction(action) {
            const actions = {
                'search': 'Searching knowledge base for relevant information...',
                'train': 'Opening training session configuration...',
                'add': 'Preparing knowledge addition interface...'
            };

            if (action === 'train') {
                openTrainingDialog();
            } else {
                addSystemMessage(`🎓 Jarvis: ${actions[action] || 'Processing knowledge command...'}`);
            }
        }

        function filesAction(action) {
            const actions = {
                'create': 'Opening Python file creation wizard...',
                'project': 'Initializing new project structure...',
                'manager': 'Launching file management interface...',
                'templates': 'Loading available code templates...'
            };
            addSystemMessage(`📁 Jarvis: ${actions[action] || 'Processing file command...'}`);
        }

        function webAction(action) {
            const actions = {
                'learning': 'Initiating web learning protocols...',
                'youtube': 'Connecting to YouTube learning system...',
                'research': 'Preparing research topic analysis...'
            };
            addSystemMessage(`🌐 Jarvis: ${actions[action] || 'Processing web command...'}`);
        }

        function toggleVoice() {
            isVoiceActive = !isVoiceActive;
            const voiceBtn = document.getElementById('voiceBtn');

            if (isVoiceActive) {
                voiceBtn.textContent = '🎤 Voice Active';
                voiceBtn.className = 'voice-btn';
                addSystemMessage('🎤 Jarvis: Voice recognition activated.');
            } else {
                voiceBtn.textContent = '🔇 Voice Inactive';
                voiceBtn.className = 'voice-btn inactive';
                addSystemMessage('🔇 Jarvis: Voice recognition deactivated.');
            }
        }

        // Training Functions
        function openTrainingDialog() {
            const duration = prompt('Training duration (e.g., "2 hours", "30 minutes"):');
            const topic = prompt('Training topic (or leave empty for general knowledge):');

            if (duration) {
                const message = `train for ${duration}${topic ? ` about ${topic}` : ''}`;
                document.getElementById('messageInput').value = message;
                sendMessage();
            }
        }

        function startProgressUpdates() {
            updateTrainingProgress();
            setInterval(updateTrainingProgress, 5000); // Update every 5 seconds
        }

        function updateTrainingProgress() {
            fetch('/api/training/progress')
                .then(response => response.json())
                .then(data => {
                    const progressFill = document.getElementById('trainingProgress');
                    const progressText = document.getElementById('progressText');

                    let maxProgress = 0;
                    let activeCount = 0;

                    if (data.active_sessions) {
                        const sessions = Object.values(data.active_sessions);
                        activeCount = sessions.length;

                        if (sessions.length > 0) {
                            maxProgress = Math.max(...sessions.map(s => s.progress || 0));
                        }
                    }

                    progressFill.style.width = `${maxProgress}%`;
                    progressText.textContent = activeCount > 0 ?
                        `${maxProgress.toFixed(1)}% (${activeCount} active)` : '0%';

                    // Add pulse effect if training is active
                    const progressBar = document.querySelector('.progress-bar');
                    if (activeCount > 0) {
                        progressBar.classList.add('pulse');
                    } else {
                        progressBar.classList.remove('pulse');
                    }
                })
                .catch(error => {
                    console.error('Error updating progress:', error);
                });
        }

        // Modal Functions
        function openSessionManager() {
            fetch('/api/training/sessions')
                .then(response => response.json())
                .then(data => {
                    const content = document.getElementById('sessionContent');
                    let html = '';

                    // Active sessions
                    if (Object.keys(data.active_sessions || {}).length > 0) {
                        html += '<h3>🚀 Active Sessions</h3>';
                        for (const [sessionId, session] of Object.entries(data.active_sessions)) {
                            html += `
                                <div style="background: var(--input-bg); padding: 10px; margin: 5px 0; border-radius: 6px;">
                                    <strong>${sessionId}</strong><br>
                                    Type: ${session.training_type}<br>
                                    Progress: ${(session.progress || 0).toFixed(1)}%<br>
                                    Knowledge: ${session.knowledge_gained || 0} items<br>
                                    <button onclick="pauseSession('${sessionId}')" style="margin: 5px 5px 0 0; padding: 5px 10px; background: var(--warning-color); color: white; border: none; border-radius: 4px;">⏸️ Pause</button>
                                    <button onclick="stopSession('${sessionId}')" style="margin: 5px 0 0 0; padding: 5px 10px; background: var(--error-color); color: white; border: none; border-radius: 4px;">⏹️ Stop</button>
                                </div>
                            `;
                        }
                    } else {
                        html += '<p>No active training sessions</p>';
                    }

                    // Recent sessions
                    if (data.recent_sessions && data.recent_sessions.length > 0) {
                        html += '<h3>📚 Recent Sessions</h3>';
                        data.recent_sessions.slice(-5).forEach(session => {
                            html += `
                                <div style="background: var(--input-bg); padding: 8px; margin: 5px 0; border-radius: 6px; opacity: 0.8;">
                                    <strong>${session.id || 'Unknown'}</strong><br>
                                    Status: ${session.status || 'completed'}<br>
                                    Knowledge: ${session.knowledge_gained || 0} items
                                </div>
                            `;
                        });
                    }

                    content.innerHTML = html || '<p>No session data available</p>';
                    openModal('sessionModal');
                })
                .catch(error => {
                    console.error('Error loading sessions:', error);
                    document.getElementById('sessionContent').innerHTML = '<p>Error loading session data</p>';
                    openModal('sessionModal');
                });
        }

        function pauseSession(sessionId) {
            fetch(`/api/training/session/${sessionId}/pause`, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    addSystemMessage(`⏸️ Jarvis: ${data.message}`);
                    openSessionManager(); // Refresh the modal
                })
                .catch(error => {
                    addSystemMessage('❌ Jarvis: Error pausing session');
                });
        }

        function stopSession(sessionId) {
            fetch(`/api/training/session/${sessionId}/stop`, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    addSystemMessage(`⏹️ Jarvis: ${data.message}`);
                    openSessionManager(); // Refresh the modal
                })
                .catch(error => {
                    addSystemMessage('❌ Jarvis: Error stopping session');
                });
        }

        function openThemeSelector() {
            openModal('themeModal');
        }

        function changeTheme(themeName) {
            fetch(`/api/theme/${themeName}`, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        applyTheme(themeName);
                        addSystemMessage(`🎨 Jarvis: Theme changed to ${themeName.replace('_', ' ')}`);
                        closeModal('themeModal');
                    }
                })
                .catch(error => {
                    addSystemMessage('❌ Jarvis: Error changing theme');
                });
        }

        function applyTheme(themeName) {
            const themes = {
                'dark_professional': {
                    '--bg-color': '#2c3e50',
                    '--panel-color': '#34495e',
                    '--accent-color': '#3498db',
                    '--text-color': '#ecf0f1'
                },
                'neon_cyber': {
                    '--bg-color': '#0a0a0a',
                    '--panel-color': '#1a1a2e',
                    '--accent-color': '#00ff41',
                    '--text-color': '#00ff41'
                },
                'matrix_green': {
                    '--bg-color': '#000000',
                    '--panel-color': '#001100',
                    '--accent-color': '#00ff00',
                    '--text-color': '#00ff00'
                },
                'electric_blue': {
                    '--bg-color': '#0f1419',
                    '--panel-color': '#1e2328',
                    '--accent-color': '#00d4ff',
                    '--text-color': '#ffffff'
                }
            };

            const theme = themes[themeName];
            if (theme) {
                const root = document.documentElement;
                Object.entries(theme).forEach(([property, value]) => {
                    root.style.setProperty(property, value);
                });
                currentTheme = themeName;
            }
        }

        function showSystemStatus() {
            fetch('/api/system/status')
                .then(response => response.json())
                .then(data => {
                    const content = document.getElementById('statusContent');
                    const html = `
                        <h3>⚙️ System Components</h3>
                        <div style="background: var(--input-bg); padding: 10px; border-radius: 6px; margin: 10px 0;">
                            Enhanced Training: ${data.enhanced_training ? '✅' : '❌'}<br>
                            Ultra Command System: ${data.ultra_command_system ? '✅' : '❌'}<br>
                            Advanced Brain: ${data.advanced_brain ? '✅' : '❌'}<br>
                            User Manager: ${data.user_manager ? '✅' : '❌'}<br>
                            File Creator: ${data.file_creator ? '✅' : '❌'}<br>
                            Web Intelligence: ${data.web_intelligence ? '✅' : '❌'}
                        </div>
                        <h3>📊 Statistics</h3>
                        <div style="background: var(--input-bg); padding: 10px; border-radius: 6px; margin: 10px 0;">
                            Active Sessions: ${data.active_sessions}<br>
                            Chat Messages: ${data.chat_messages}<br>
                            Current Theme: ${data.current_theme}<br>
                            Last Update: ${new Date(data.timestamp).toLocaleString()}
                        </div>
                    `;
                    content.innerHTML = html;
                    openModal('statusModal');
                })
                .catch(error => {
                    document.getElementById('statusContent').innerHTML = '<p>Error loading system status</p>';
                    openModal('statusModal');
                });
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'flex';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function refreshInterface() {
            location.reload();
        }

        function showHelp() {
            addSystemMessage(`
                🤖 Jarvis: Available commands and features:

                💬 Chat Commands:
                • "train for X hours about [topic]" - Start training
                • "show training progress" - View current progress
                • "stop training" - Stop active sessions

                🎮 Interface:
                • Click progress bar to open Session Manager
                • Use panel buttons for quick actions
                • Tap theme button (🎨) to change appearance

                📱 Mobile Optimized:
                • Designed for iPhone 16+ screen size
                • Touch-friendly interface
                • Responsive design for all orientations
            `);
        }

        // Service Worker Registration
        function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/static/sw.js')
                    .then(function(registration) {
                        console.log('✅ Service Worker registered:', registration.scope);

                        // Check for updates
                        registration.addEventListener('updatefound', function() {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', function() {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New version available
                                    showNotification('🔄 New version available! Refresh to update.', 'info');
                                }
                            });
                        });
                    })
                    .catch(function(error) {
                        console.log('❌ Service Worker registration failed:', error);
                    });
            }
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', function(e) {
            console.log('💾 PWA install prompt available');
            e.preventDefault();
            deferredPrompt = e;

            // Show install button or notification
            showNotification('📱 Add JARVIS to your home screen for the best experience!', 'info');
        });

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then(function(choiceResult) {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('✅ User accepted PWA install');
                        showNotification('🎉 JARVIS installed successfully!', 'success');
                    } else {
                        console.log('❌ User dismissed PWA install');
                    }
                    deferredPrompt = null;
                });
            }
        }

        // Notification System
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div>${message}</div>
                <button onclick="this.parentElement.remove()" style="background: none; border: none; color: white; float: right; cursor: pointer;">&times;</button>
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);

            // Auto-hide after 5 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
