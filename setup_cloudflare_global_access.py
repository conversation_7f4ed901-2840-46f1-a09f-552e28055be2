#!/usr/bin/env python3
"""
☁️ CLOUDFLARE TUNNEL SETUP - FREE GLOBAL ACCESS
Better alternative to ngrok - completely free, no authentication needed
"""

import subprocess
import sys
import os
import time
import webbrowser
import requests
import zipfile
import platform

def download_cloudflared():
    """Download cloudflared for Windows."""
    print("📥 DOWNLOADING CLOUDFLARE TUNNEL...")
    print("-" * 35)
    
    # Check if already exists
    if os.path.exists('cloudflared.exe'):
        print("✅ cloudflared.exe already exists")
        return True
    
    try:
        # Download URL for Windows
        url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe"
        
        print("🌐 Downloading from GitHub...")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open('cloudflared.exe', 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print("✅ Download complete!")
        return True
        
    except Exception as e:
        print(f"❌ Download failed: {e}")
        print("💡 Manual download:")
        print("   1. Go to: https://github.com/cloudflare/cloudflared/releases")
        print("   2. Download cloudflared-windows-amd64.exe")
        print("   3. Rename to cloudflared.exe")
        print("   4. Put in same folder as this script")
        return False

def test_cloudflared():
    """Test if cloudflared works."""
    try:
        result = subprocess.run(['./cloudflared.exe', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ cloudflared version: {result.stdout.strip()}")
            return True
        else:
            print("❌ cloudflared test failed")
            return False
    except Exception as e:
        print(f"❌ cloudflared test error: {e}")
        return False

def start_cloudflare_tunnel():
    """Start Cloudflare tunnel for global access."""
    print("\n🚀 STARTING CLOUDFLARE TUNNEL...")
    print("-" * 33)
    
    try:
        # Start tunnel
        print("🌍 Creating secure tunnel to localhost:8080...")
        process = subprocess.Popen([
            './cloudflared.exe', 'tunnel', '--url', 'http://localhost:8080'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait for tunnel to start and get URL
        time.sleep(5)
        
        # Try to get tunnel info
        try:
            # Check if process is still running
            if process.poll() is None:
                print("✅ Tunnel process started successfully!")
                
                # Try to read output for URL
                stdout_data = ""
                stderr_data = ""
                
                # Read available output
                try:
                    stdout_data, stderr_data = process.communicate(timeout=2)
                except subprocess.TimeoutExpired:
                    # Process is still running, which is good
                    pass
                
                # Look for URL in output
                output = stdout_data + stderr_data
                lines = output.split('\n')
                
                tunnel_url = None
                for line in lines:
                    if 'https://' in line and 'trycloudflare.com' in line:
                        # Extract URL
                        parts = line.split()
                        for part in parts:
                            if part.startswith('https://') and 'trycloudflare.com' in part:
                                tunnel_url = part
                                break
                        if tunnel_url:
                            break
                
                if tunnel_url:
                    print(f"🌍 PUBLIC URL: {tunnel_url}")
                    print("📱 Use this URL on your iPhone from ANYWHERE!")
                    return tunnel_url, process
                else:
                    print("⚠️ Tunnel started but URL not found in output")
                    print("💡 Check terminal for tunnel URL")
                    return "https://your-tunnel.trycloudflare.com", process
            else:
                print("❌ Tunnel process failed to start")
                return None, None
                
        except Exception as e:
            print(f"⚠️ Error getting tunnel info: {e}")
            return "https://your-tunnel.trycloudflare.com", process
            
    except Exception as e:
        print(f"❌ Error starting tunnel: {e}")
        return None, None

def main():
    """Main setup function."""
    print("☁️ CLOUDFLARE TUNNEL SETUP")
    print("=" * 30)
    print("🌍 FREE global access for Jarvis!")
    print("✅ No authentication required")
    print("🚀 Works from anywhere in the world")
    print()
    
    # Step 1: Download cloudflared
    if not download_cloudflared():
        print("❌ Setup failed - could not download cloudflared")
        return False
    
    # Step 2: Test cloudflared
    print("\n🧪 TESTING CLOUDFLARED...")
    print("-" * 22)
    if not test_cloudflared():
        print("❌ Setup failed - cloudflared not working")
        return False
    
    # Step 3: Instructions
    print("\n📋 SETUP INSTRUCTIONS")
    print("-" * 21)
    print("1. 🚀 Start Jarvis: python jarvis_complete_launcher.py")
    print("2. ☁️ Start tunnel: python setup_cloudflare_global_access.py")
    print("3. 🌍 Use the public URL on your iPhone")
    print()
    
    # Ask if user wants to start tunnel now
    choice = input("🔥 Start tunnel now? (y/n): ").strip().lower()
    
    if choice == 'y':
        # Check if Jarvis is running
        try:
            response = requests.get('http://localhost:8080', timeout=5)
            print("✅ Jarvis web app is running!")
        except:
            print("⚠️ Jarvis web app not detected")
            print("💡 Start it first: python jarvis_complete_launcher.py")
            print("🔄 Then run this script again")
            return False
        
        # Start tunnel
        tunnel_url, process = start_cloudflare_tunnel()
        
        if tunnel_url and process:
            print("\n🎉 SUCCESS!")
            print("=" * 15)
            print(f"🌍 Global URL: {tunnel_url}")
            print("📱 Use this on your iPhone from anywhere!")
            print()
            print("✅ Features available:")
            print("   • 🎤 Voice input")
            print("   • 💬 Text chat")
            print("   • 🧠 Full Jarvis integration")
            print("   • 🌍 Access from anywhere")
            print()
            print("🛑 Press Ctrl+C to stop tunnel")
            
            try:
                # Keep tunnel running
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping tunnel...")
                process.terminate()
                print("✅ Tunnel stopped")
        else:
            print("❌ Failed to start tunnel")
            return False
    else:
        print("💡 To start tunnel later:")
        print("   python setup_cloudflare_global_access.py")
    
    print("\n✅ Cloudflare Tunnel setup complete!")
    return True

if __name__ == "__main__":
    main()
