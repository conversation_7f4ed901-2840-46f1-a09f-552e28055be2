#!/usr/bin/env python3
"""
🌐 Quick ngrok Setup - Instant Global Access
Creates a public URL for your Jarvis Web App
"""

import subprocess
import time
import requests
import webbrowser
import os

def download_ngrok():
    """Instructions to download ngrok."""
    print("📥 NGROK SETUP - INSTANT GLOBAL ACCESS")
    print("=" * 50)
    print("ngrok creates a public URL that works from anywhere!")
    print()
    print("🔗 Download ngrok:")
    print("1. Go to: https://ngrok.com/download")
    print("2. Download 'ngrok for Windows'")
    print("3. Extract ngrok.exe to this folder")
    print("4. Run this script again")
    print()
    print("⚡ OR use this direct link:")
    print("https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-windows-amd64.zip")
    
    # Try to open download page
    try:
        webbrowser.open("https://ngrok.com/download")
    except:
        pass

def check_ngrok():
    """Check if ngrok is available."""
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ ngrok found: {result.stdout.strip()}")
            return True
    except:
        pass
    
    # Check if ngrok.exe is in current directory
    if os.path.exists('ngrok.exe'):
        try:
            result = subprocess.run(['./ngrok.exe', 'version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ ngrok.exe found: {result.stdout.strip()}")
                return True
        except:
            pass
    
    return False

def start_ngrok_tunnel():
    """Start ngrok tunnel."""
    print("🚀 Starting ngrok tunnel...")
    print("⚠️ This creates a PUBLIC URL - anyone with the link can access!")
    print()
    
    # Try different ngrok commands
    ngrok_commands = ['ngrok', './ngrok.exe', 'ngrok.exe']
    
    for cmd in ngrok_commands:
        try:
            # Start ngrok
            process = subprocess.Popen([
                cmd, 'http', '8080', '--log=stdout'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            print(f"🔄 Starting tunnel with {cmd}...")
            time.sleep(5)  # Wait for ngrok to start
            
            # Get the public URL
            try:
                response = requests.get('http://localhost:4040/api/tunnels', timeout=3)
                data = response.json()
                
                if data.get('tunnels'):
                    public_url = data['tunnels'][0]['public_url']
                    print()
                    print("🎉 SUCCESS! Your Jarvis Web App is now PUBLIC!")
                    print("=" * 60)
                    print(f"🔗 PUBLIC URL: {public_url}")
                    print("=" * 60)
                    print()
                    print("📱 USE THIS URL ON YOUR IPHONE:")
                    print(f"   {public_url}")
                    print()
                    print("🌍 This URL works from ANYWHERE in the world!")
                    print("📱 Works on any device, any network")
                    print("⚠️ SECURITY: This URL is public - anyone can access it")
                    print()
                    print("🛑 Press Ctrl+C to stop the tunnel")
                    print("🔒 URL will stop working when you stop this script")
                    
                    # Try to open in browser
                    try:
                        webbrowser.open(public_url)
                    except:
                        pass
                    
                    return process, public_url
                    
            except Exception as e:
                print(f"❌ Error getting tunnel URL: {e}")
                print("🔧 Try opening http://localhost:4040 in your browser")
                
        except Exception as e:
            print(f"❌ Failed to start with {cmd}: {e}")
            continue
    
    return None, None

def main():
    """Main function."""
    print("🌐 INSTANT GLOBAL ACCESS FOR JARVIS WEB APP")
    print("=" * 50)
    
    # Check if Jarvis is running
    try:
        response = requests.get('http://localhost:8080', timeout=3)
        print("✅ Jarvis Web App is running on port 8080")
    except:
        print("❌ Jarvis Web App is NOT running!")
        print("🔧 Start it first: python jarvis_web_app.py")
        return
    
    print()
    
    # Check for ngrok
    if not check_ngrok():
        download_ngrok()
        return
    
    # Start tunnel
    process, url = start_ngrok_tunnel()
    
    if process and url:
        try:
            # Keep running
            print("\n⏳ Tunnel is running... (Ctrl+C to stop)")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping tunnel...")
            process.terminate()
            print("👋 Tunnel stopped - URL no longer works")
    else:
        print("❌ Failed to create tunnel")
        print()
        print("🔧 MANUAL SETUP:")
        print("1. Download ngrok from https://ngrok.com/download")
        print("2. Extract ngrok.exe to this folder")
        print("3. Open Command Prompt here")
        print("4. Run: ngrok http 8080")
        print("5. Use the provided URL on your iPhone")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled")
    except Exception as e:
        print(f"\n❌ Error: {e}")
