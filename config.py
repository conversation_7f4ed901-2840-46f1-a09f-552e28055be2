# config.py

RESEMBLE_API_KEY = "9ACxs2JzWucrlychgYOZ9Qtt"  # Replace with your actual Resemble AI API key
RESEMBLE_VOICE_ID = "41b99669"  # Alex voice - Replace with your preferred voice ID
LLAMA_MODEL_PATH = "C:\\Users\\<USER>\\Desktop\\llama server\\models\\mistral-7b-instruct-v0.1.Q4_K_M.gguf" #Path to the Llama model

# Network Configuration Options:
# Option 1: Use your current IP address (recommended for local network access)
HOST = "************"  # Your actual IP address from ipconfig

# Option 2: Use localhost (uncomment this line and comment the line above for local-only access)
# HOST = "127.0.0.1"  # Localhost - only accessible from this computer

# Option 3: Use all interfaces (uncomment this line for maximum compatibility)
# HOST = "0.0.0.0"  # Listen on all available network interfaces

PORT = 8003 #Port number