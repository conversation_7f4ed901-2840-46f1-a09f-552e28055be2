Metadata-Version: 2.1
Name: py-cpuinfo
Version: 9.0.0
Summary: Get CPU info with pure Python
Home-page: https://github.com/workhorsy/py-cpuinfo
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Topic :: Utilities
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
License-File: LICENSE

py-cpuinfo
==========


Py-cpuinfo gets CPU info with pure Python. Py-cpuinfo should work
without any extra programs or libraries, beyond what your OS provides.
It does not require any compilation(C/C++, assembly, et cetera) to use.
It works with Python 3.

Documentation can be viewed here: https://github.com/workhorsy/py-cpuinfo


