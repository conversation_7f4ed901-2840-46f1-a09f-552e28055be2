#!/usr/bin/env python3
"""
🔧 FIX NGROK ERR_NGROK_8012 ERROR
Quick fix for ngrok authentication to enable global Jarvis access
"""

import subprocess
import webbrowser
import sys

def main():
    """Fix ngrok authentication error."""
    print("🔧 FIXING NGROK ERR_NGROK_8012 ERROR")
    print("=" * 40)
    print()
    
    print("❌ ERR_NGROK_8012 means ngrok needs authentication")
    print("✅ This is easy to fix - takes 2 minutes!")
    print()
    
    print("🚀 QUICK FIX STEPS:")
    print("1. 🌐 Sign up for FREE ngrok account")
    print("2. 🔑 Get your authentication token") 
    print("3. 🔧 Configure ngrok with token")
    print("4. ✅ Global access works!")
    print()
    
    # Check if ngrok is installed
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ ngrok not found!")
            print("💡 Download from: https://ngrok.com/download")
            return
    except:
        print("❌ ngrok not installed!")
        print("💡 Download from: https://ngrok.com/download")
        return
    
    print("✅ ngrok is installed")
    print()
    
    # Check current auth status
    try:
        result = subprocess.run(['ngrok', 'config', 'check'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ ngrok is already authenticated!")
            print("🎉 You should be able to use global access now")
            print("🚀 Run: python jarvis_complete_launcher.py")
            return
    except:
        pass
    
    print("🔑 NGROK AUTHENTICATION NEEDED")
    print("-" * 32)
    print()
    
    # Open ngrok signup
    print("🌐 Opening ngrok signup page...")
    try:
        webbrowser.open("https://ngrok.com/signup")
        print("✅ Browser opened to: https://ngrok.com/signup")
    except:
        print("💡 Manually go to: https://ngrok.com/signup")
    
    print()
    print("📋 FOLLOW THESE STEPS:")
    print("1. 📝 Create FREE account (30 seconds)")
    print("2. 🔑 Go to dashboard → 'Your Authtoken'")
    print("3. 📋 Copy the token (looks like: 2abc123def456ghi789jkl)")
    print("4. 🔧 Come back here and enter it")
    print()
    
    # Wait for user to get token
    input("⏸️ Press Enter when you have your token ready...")
    print()
    
    # Get token from user
    while True:
        token = input("🔑 Paste your ngrok authtoken here: ").strip()
        
        if not token:
            print("❌ No token entered, try again")
            continue
            
        if len(token) < 20:
            print("❌ Token seems too short, make sure you copied the full token")
            continue
            
        # Try to configure ngrok
        print("🔧 Configuring ngrok...")
        try:
            result = subprocess.run(['ngrok', 'config', 'add-authtoken', token], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ SUCCESS! ngrok authentication configured!")
                print()
                
                # Verify it worked
                verify_result = subprocess.run(['ngrok', 'config', 'check'], 
                                             capture_output=True, text=True)
                if verify_result.returncode == 0:
                    print("🎉 VERIFICATION PASSED!")
                    print("🌍 Global access is now enabled!")
                    print()
                    print("🚀 NEXT STEPS:")
                    print("1. Run: python jarvis_complete_launcher.py")
                    print("2. Look for the public ngrok URL")
                    print("3. Use that URL on your iPhone from anywhere!")
                    print()
                    print("📱 Example: https://abc123.ngrok.io")
                    print("✅ No more ERR_NGROK_8012 error!")
                    break
                else:
                    print("⚠️ Configuration saved but verification failed")
                    print("💡 Try running the launcher anyway")
                    break
            else:
                print(f"❌ Configuration failed: {result.stderr}")
                print("💡 Make sure you copied the complete token")
                
        except Exception as e:
            print(f"❌ Error configuring ngrok: {e}")
            print("💡 Try copying the token again")

if __name__ == "__main__":
    main()
