#!/usr/bin/env python3
"""
📱 MOBILE ACCESS HELPER
Simple solution for iPhone access without ngrok authentication issues
"""

import socket
import subprocess
import sys
import os

def get_local_ip():
    """Get the local IP address."""
    try:
        # Connect to a remote address to get local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        try:
            # Fallback method
            hostname = socket.gethostname()
            return socket.gethostbyname(hostname)
        except:
            return "127.0.0.1"

def check_port_open(ip, port):
    """Check if a port is open."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result == 0
    except:
        return False

def generate_url_display(url):
    """Generate a nice display for the URL."""
    try:
        # Create a simple text box around the URL
        url_length = len(url)
        border = "+" + "-" * (url_length + 2) + "+"

        display = f"""
{border}
| {url} |
{border}

📱 COPY THIS URL TO YOUR IPHONE:
{url}

💡 Or type this in Safari address bar:
{url}
        """
        return display.strip()
    except:
        return f"URL: {url}"

def check_firewall_status():
    """Check Windows firewall status for port 8080."""
    try:
        # Check if port 8080 is allowed through firewall
        result = subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'show', 'rule', 
            'name=all', 'dir=in', 'protocol=tcp', 'localport=8080'
        ], capture_output=True, text=True)
        
        if 'No rules match' in result.stdout:
            return False
        return True
    except:
        return None

def create_firewall_rule():
    """Create firewall rule for port 8080."""
    try:
        print("🔥 Creating Windows Firewall rule for port 8080...")
        result = subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'add', 'rule',
            'name=Jarvis Web App', 'dir=in', 'action=allow',
            'protocol=TCP', 'localport=8080'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Firewall rule created successfully!")
            return True
        else:
            print(f"❌ Failed to create firewall rule: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error creating firewall rule: {e}")
        return False

def main():
    """Main mobile access helper."""
    print("📱 JARVIS MOBILE ACCESS HELPER")
    print("=" * 35)
    print("🔧 Setting up iPhone access without ngrok...")
    print()
    
    # Get network information
    local_ip = get_local_ip()
    port = 8080
    
    print("🌐 NETWORK INFORMATION")
    print("-" * 22)
    print(f"🖥️ Computer IP: {local_ip}")
    print(f"🔌 Port: {port}")
    print()
    
    # Check if Jarvis web app is running
    if check_port_open(local_ip, port):
        print("✅ Jarvis web app is running!")
    else:
        print("⚠️ Jarvis web app not detected")
        print("💡 Start it with: python jarvis_complete_launcher.py")
        print()
    
    # Check firewall
    print("🔥 FIREWALL CHECK")
    print("-" * 16)
    
    firewall_status = check_firewall_status()
    if firewall_status is None:
        print("⚠️ Cannot check firewall status")
    elif firewall_status:
        print("✅ Port 8080 is allowed through firewall")
    else:
        print("❌ Port 8080 blocked by firewall")
        print("🔧 Attempting to create firewall rule...")
        
        if create_firewall_rule():
            print("✅ Firewall configured for mobile access")
        else:
            print("⚠️ Manual firewall configuration needed")
            print("💡 Manually allow port 8080 in Windows Firewall")
    
    print()
    
    # Mobile access URLs
    mobile_url = f"http://{local_ip}:8080"
    
    print("📱 MOBILE ACCESS URLS")
    print("-" * 21)
    print(f"🌐 Main URL: {mobile_url}")
    print(f"🏠 Localhost: http://127.0.0.1:8080")
    print(f"🖥️ Computer: http://localhost:8080")
    print()
    
    # Generate URL display
    print("📱 IPHONE ACCESS URL")
    print("-" * 20)
    url_display = generate_url_display(mobile_url)
    print(url_display)
    
    print()
    
    # Instructions
    print("📋 IPHONE SETUP INSTRUCTIONS")
    print("-" * 29)
    print("1. 🔗 Make sure your iPhone is on the SAME WiFi network")
    print("2. 📷 Scan the QR code above with iPhone camera")
    print("3. 🌐 Or manually open Safari and go to:")
    print(f"   {mobile_url}")
    print("4. 🎤 Use the voice button to speak to Jarvis")
    print("5. 💬 Or type messages in the chat interface")
    print()
    
    # Troubleshooting
    print("🔧 TROUBLESHOOTING")
    print("-" * 15)
    print("❌ If iPhone can't connect:")
    print("   • Check both devices are on same WiFi")
    print("   • Disable VPN on iPhone")
    print("   • Try restarting WiFi on both devices")
    print("   • Check Windows Firewall settings")
    print()
    print("❌ If voice doesn't work:")
    print("   • Allow microphone access in Safari")
    print("   • Try Chrome browser instead")
    print("   • Check Safari settings for microphone")
    print()
    
    # Alternative solutions
    print("🌍 ALTERNATIVE GLOBAL ACCESS")
    print("-" * 28)
    print("💡 For access from anywhere (not just same WiFi):")
    print("   • Use Tailscale (recommended): https://tailscale.com")
    print("   • Set up router port forwarding")
    print("   • Use TeamViewer or similar remote access")
    print("   • Set up ngrok with authentication token")
    print()
    
    print("✅ Mobile access helper complete!")
    print(f"🎯 Your iPhone URL: {mobile_url}")

if __name__ == "__main__":
    main()
