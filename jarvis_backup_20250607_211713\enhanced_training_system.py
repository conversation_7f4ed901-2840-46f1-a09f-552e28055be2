"""
Enhanced Training System for Jarvis
===================================

Advanced training system with custom duration, feedback integration,
and self-improvement capabilities.

Features:
- Custom training duration (minutes to days)
- User feedback integration
- Response quality improvement
- Continuous learning from errors
- Performance tracking and optimization

Author: Jarvis AI System
Version: 2.0 (Enhanced with Feedback)
"""

import asyncio
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import sqlite3
from knowledge_trainer import JarvisKnowledgeTrainer

class EnhancedTrainingSystem:
    """Enhanced training system with feedback and custom duration."""
    
    def __init__(self, gui_callback=None):
        self.gui_callback = gui_callback
        self.knowledge_trainer = JarvisKnowledgeTrainer()
        self.training_active = False
        self.current_session = None
        
        # Initialize feedback database
        self.init_feedback_database()
        
        # Training metrics
        self.training_metrics = {
            'total_training_time': 0.0,
            'sessions_completed': 0,
            'feedback_received': 0,
            'improvements_applied': 0,
            'response_quality_score': 75.0
        }

        # Recent sessions tracking
        self.recent_sessions = []
        
        print("🎓 Enhanced Training System initialized - Ready for custom training!")
    
    def init_feedback_database(self):
        """Initialize database for storing user feedback and improvements."""
        try:
            # Ensure database directory exists
            import os
            db_dir = os.path.dirname(os.path.abspath('jarvis_feedback.db'))
            if not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)

            self.feedback_db = sqlite3.connect('jarvis_feedback.db', check_same_thread=False)
            cursor = self.feedback_db.cursor()

            # Create feedback table with better structure
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_feedback (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    user_input TEXT,
                    jarvis_response TEXT,
                    feedback_type TEXT NOT NULL,
                    feedback_text TEXT,
                    severity INTEGER DEFAULT 3,
                    resolved BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create improvements table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS response_improvements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    original_response TEXT,
                    improved_response TEXT,
                    improvement_reason TEXT,
                    feedback_id INTEGER,
                    applied BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (feedback_id) REFERENCES user_feedback (id)
                )
            ''')

            # Create training sessions table with enhanced tracking
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS training_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    duration_hours REAL,
                    training_type TEXT,
                    topics_covered INTEGER DEFAULT 0,
                    knowledge_gained INTEGER DEFAULT 0,
                    quality_improvement REAL DEFAULT 0.0,
                    status TEXT DEFAULT 'active',
                    custom_topics TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create performance metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    response_time REAL,
                    accuracy_score REAL,
                    user_satisfaction INTEGER,
                    session_id TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            self.feedback_db.commit()

            # Test database connection
            cursor.execute('SELECT COUNT(*) FROM user_feedback')
            feedback_count = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM training_sessions')
            session_count = cursor.fetchone()[0]

            print(f"📊 Enhanced feedback database initialized successfully!")
            print(f"   📝 Feedback entries: {feedback_count}")
            print(f"   🎓 Training sessions: {session_count}")

        except Exception as e:
            print(f"❌ Critical database error: {e}")
            print(f"   Attempting to create backup database...")
            try:
                # Create backup database with minimal structure
                self.feedback_db = sqlite3.connect(':memory:', check_same_thread=False)
                cursor = self.feedback_db.cursor()
                cursor.execute('''
                    CREATE TABLE user_feedback (
                        id INTEGER PRIMARY KEY,
                        timestamp TEXT,
                        feedback_type TEXT,
                        feedback_text TEXT,
                        resolved BOOLEAN DEFAULT FALSE
                    )
                ''')
                cursor.execute('''
                    CREATE TABLE training_sessions (
                        id INTEGER PRIMARY KEY,
                        start_time TEXT,
                        training_type TEXT,
                        status TEXT DEFAULT 'active'
                    )
                ''')
                self.feedback_db.commit()
                print("✅ Backup in-memory database created")
            except Exception as backup_error:
                print(f"❌ Backup database failed: {backup_error}")
                self.feedback_db = None
    
    async def start_custom_training(self, duration_hours: float, training_type: str = 'comprehensive',
                                  custom_topics: Optional[List[str]] = None) -> Dict:
        """Start training with custom duration and topics."""
        if self.training_active:
            return {'success': False, 'message': 'Training already in progress'}
        
        self.training_active = True
        session_id = int(time.time())
        
        # Convert hours to more readable format
        if duration_hours < 1:
            duration_text = f"{duration_hours * 60:.0f} minutes"
        elif duration_hours < 24:
            duration_text = f"{duration_hours:.1f} hours"
        else:
            days = duration_hours / 24
            duration_text = f"{days:.1f} days"
        
        print(f"🚀 Starting {training_type} training for {duration_text}")
        if self.gui_callback:
            self.gui_callback(f"🚀 Jarvis: Starting {training_type} training for {duration_text}!")
            self.gui_callback("📚 Jarvis: I'll continuously learn and improve during this session.")
        
        try:
            # Record session start
            session_data = {
                'id': session_id,
                'start_time': datetime.now().isoformat(),
                'duration_hours': duration_hours,
                'training_type': training_type,
                'custom_topics': custom_topics or [],
                'progress': 0.0,
                'knowledge_gained': 0,
                'topics_covered': 0
            }
            
            self.current_session = session_data
            
            # Start the training process
            result = await self.knowledge_trainer.start_massive_training(
                training_type=training_type,
                duration_hours=duration_hours,
                custom_topics=custom_topics,
                feedback_integration=True
            )
            
            # Update session data
            session_data['end_time'] = datetime.now().isoformat()
            session_data['knowledge_gained'] = result.get('knowledge_items_added', 0)
            session_data['topics_covered'] = len(result.get('categories_covered', []))
            
            # Store session in database
            self.store_training_session(session_data)

            # Add to recent sessions (keep last 10)
            self.recent_sessions.append(session_data)
            if len(self.recent_sessions) > 10:
                self.recent_sessions = self.recent_sessions[-10:]

            # Update metrics
            self.training_metrics['total_training_time'] += duration_hours
            self.training_metrics['sessions_completed'] += 1
            
            # Apply any pending feedback improvements
            improvements_applied = await self.apply_feedback_improvements()
            if improvements_applied > 0:
                self.training_metrics['improvements_applied'] += improvements_applied
                if self.gui_callback:
                    self.gui_callback(f"🔧 Jarvis: Applied {improvements_applied} improvements based on your feedback!")

            # Save learned knowledge to persistent storage
            await self.save_training_knowledge(session_data, result)

            # Update Jarvis's code with new capabilities if significant learning occurred
            if result.get('knowledge_items_added', 0) > 10:  # Threshold for code updates
                await self.update_jarvis_capabilities(result)

            self.training_active = False
            self.current_session = None

            if self.gui_callback:
                self.gui_callback(f"✅ Jarvis: Training completed! Gained {result.get('knowledge_items_added', 0)} knowledge items.")
                self.gui_callback("🧠 Jarvis: I feel smarter already! My responses should be more accurate now.")
                self.gui_callback("💾 Jarvis: Knowledge has been saved and integrated into my core systems.")
            
            return {
                'success': True,
                'session_data': session_data,
                'training_result': result,
                'improvements_applied': improvements_applied
            }
            
        except Exception as e:
            self.training_active = False
            self.current_session = None
            error_msg = f"Training error: {e}"
            print(f"❌ {error_msg}")
            if self.gui_callback:
                self.gui_callback(f"❌ Jarvis: {error_msg}")
            return {'success': False, 'error': error_msg}
    
    def store_training_session(self, session_data: Dict):
        """Store training session data in database."""
        if self.feedback_db:
            try:
                cursor = self.feedback_db.cursor()
                cursor.execute('''
                    INSERT INTO training_sessions 
                    (start_time, end_time, duration_hours, training_type, topics_covered, knowledge_gained, quality_improvement)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    session_data['start_time'],
                    session_data.get('end_time', ''),
                    session_data['duration_hours'],
                    session_data['training_type'],
                    session_data.get('topics_covered', 0),
                    session_data.get('knowledge_gained', 0),
                    5.0  # Default quality improvement
                ))
                self.feedback_db.commit()
            except Exception as e:
                print(f"⚠️ Error storing session: {e}")
    
    def receive_feedback(self, user_input: str, jarvis_response: str, 
                        feedback_type: str, feedback_text: str, severity: int = 3) -> bool:
        """Receive and store user feedback about Jarvis responses."""
        try:
            if self.feedback_db:
                cursor = self.feedback_db.cursor()
                cursor.execute('''
                    INSERT INTO user_feedback 
                    (timestamp, user_input, jarvis_response, feedback_type, feedback_text, severity)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    datetime.now().isoformat(),
                    user_input,
                    jarvis_response,
                    feedback_type,
                    feedback_text,
                    severity
                ))
                self.feedback_db.commit()
                
                self.training_metrics['feedback_received'] += 1
                
                # Generate immediate improvement if possible
                improvement = self.generate_immediate_improvement(
                    user_input, jarvis_response, feedback_type, feedback_text
                )
                
                if improvement and self.gui_callback:
                    self.gui_callback(f"🔧 Jarvis: Thank you for the feedback! I'll {improvement}")
                
                return True
                
        except Exception as e:
            print(f"⚠️ Error storing feedback: {e}")
            
        return False
    
    def generate_immediate_improvement(self, user_input: str, jarvis_response: str,
                                     feedback_type: str, feedback_text: str) -> Optional[str]:
        """Generate immediate improvement based on feedback."""
        improvements = {
            'too_long': "make my responses more concise and to the point",
            'too_short': "provide more detailed and comprehensive responses",
            'incorrect': "verify information more carefully before responding",
            'unclear': "explain things more clearly and use simpler language",
            'irrelevant': "stay more focused on your specific questions",
            'tone': "adjust my communication style to be more appropriate",
            'missing_info': "include more relevant details in my responses",
            'repetitive': "avoid repeating information and be more varied"
        }
        
        return improvements.get(feedback_type.lower())
    
    async def apply_feedback_improvements(self) -> int:
        """Apply improvements based on accumulated feedback."""
        if not self.feedback_db:
            return 0
        
        try:
            cursor = self.feedback_db.cursor()
            cursor.execute('''
                SELECT id, feedback_type, feedback_text, severity 
                FROM user_feedback 
                WHERE resolved = FALSE
                ORDER BY severity DESC, timestamp DESC
                LIMIT 10
            ''')
            
            pending_feedback = cursor.fetchall()
            improvements_applied = 0
            
            for feedback_id, feedback_type, feedback_text, severity in pending_feedback:
                # Generate improvement
                improvement = self.create_response_improvement(feedback_type, feedback_text, severity)
                
                if improvement:
                    # Store improvement
                    cursor.execute('''
                        INSERT INTO response_improvements 
                        (timestamp, improvement_reason, feedback_id)
                        VALUES (?, ?, ?)
                    ''', (
                        datetime.now().isoformat(),
                        improvement,
                        feedback_id
                    ))
                    
                    # Mark feedback as resolved
                    cursor.execute('''
                        UPDATE user_feedback SET resolved = TRUE WHERE id = ?
                    ''', (feedback_id,))
                    
                    improvements_applied += 1
            
            self.feedback_db.commit()
            
            # Update response quality score
            if improvements_applied > 0:
                quality_boost = min(improvements_applied * 2, 10)
                self.training_metrics['response_quality_score'] = min(
                    self.training_metrics['response_quality_score'] + quality_boost, 100
                )
            
            return improvements_applied
            
        except Exception as e:
            print(f"⚠️ Error applying improvements: {e}")
            return 0
    
    def create_response_improvement(self, feedback_type: str, feedback_text: str, severity: int) -> Optional[str]:
        """Create specific improvement based on feedback."""
        improvements = {
            'too_long': f"Reduce response length by {severity * 10}% and focus on key points",
            'too_short': f"Expand responses with {severity} additional relevant details",
            'incorrect': f"Implement fact-checking for {feedback_text} type information",
            'unclear': f"Simplify language and add explanations for {feedback_text}",
            'irrelevant': f"Improve topic relevance detection for {feedback_text} contexts",
            'tone': f"Adjust communication style to be more {feedback_text}",
            'missing_info': f"Include {feedback_text} information in similar responses",
            'repetitive': f"Add response variation for {feedback_text} type queries"
        }
        
        return improvements.get(feedback_type.lower())
    
    def get_training_progress(self) -> Dict:
        """Get current training progress and metrics."""
        progress = {
            'training_active': self.training_active,
            'current_session': self.current_session,
            'metrics': self.training_metrics.copy()
        }
        
        if self.current_session:
            # Calculate progress
            start_time = datetime.fromisoformat(self.current_session['start_time'])
            elapsed = (datetime.now() - start_time).total_seconds() / 3600  # hours
            total_duration = self.current_session['duration_hours']
            progress_percent = min((elapsed / total_duration) * 100, 100)
            
            progress['current_session']['progress'] = progress_percent
            progress['current_session']['elapsed_hours'] = elapsed
            progress['current_session']['remaining_hours'] = max(total_duration - elapsed, 0)
        
        return progress

    def get_recent_activity(self) -> List[str]:
        """Get recent training activity for display in GUI."""
        activities = []

        try:
            if self.training_active and self.current_session:
                # Current session activity
                session = self.current_session
                start_time = datetime.fromisoformat(session['start_time'])
                elapsed = (datetime.now() - start_time).total_seconds() / 3600

                activities.append(f"🔄 Training in progress: {session.get('training_type', 'Unknown')} ({elapsed:.1f}h)")
                activities.append(f"📚 Topics covered: {session.get('topics_covered', 0)}")
                activities.append(f"🧠 Knowledge gained: {session.get('knowledge_gained', 0)} items")
            else:
                # Recent completed sessions
                if hasattr(self, 'recent_sessions'):
                    for session in self.recent_sessions[-3:]:  # Last 3 sessions
                        session_type = session.get('training_type', 'Unknown')
                        knowledge_gained = session.get('knowledge_gained', 0)
                        activities.append(f"✅ Completed {session_type} training (+{knowledge_gained} items)")

                # Training metrics
                metrics = self.training_metrics
                activities.append(f"📊 Total sessions: {metrics.get('sessions_completed', 0)}")
                activities.append(f"⏱️ Total training time: {metrics.get('total_training_time', 0):.1f}h")
                activities.append(f"🎯 Response quality: {metrics.get('response_quality_score', 75):.1f}/100")

        except Exception as e:
            activities.append(f"⚠️ Error retrieving activity: {e}")

        return activities if activities else ["📝 No recent training activity"]

    async def save_training_knowledge(self, session_data: Dict, training_result: Dict):
        """Save learned knowledge to persistent storage and update Jarvis's knowledge base."""
        try:
            import json
            import os
            from datetime import datetime

            # Create knowledge persistence directory
            knowledge_dir = "jarvis_knowledge_persistence"
            if not os.path.exists(knowledge_dir):
                os.makedirs(knowledge_dir)

            # Save session knowledge
            session_file = f"{knowledge_dir}/training_session_{session_data['id']}.json"
            knowledge_data = {
                'session_info': session_data,
                'training_result': training_result,
                'knowledge_items': training_result.get('knowledge_items', []),
                'categories_learned': training_result.get('categories_covered', []),
                'saved_at': datetime.now().isoformat(),
                'integration_status': 'pending'
            }

            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(knowledge_data, f, indent=2)

            # Update master knowledge index
            index_file = f"{knowledge_dir}/knowledge_index.json"
            if os.path.exists(index_file):
                with open(index_file, 'r', encoding='utf-8') as f:
                    index = json.load(f)
            else:
                index = {'sessions': [], 'total_knowledge_items': 0, 'last_updated': ''}

            index['sessions'].append({
                'session_id': session_data['id'],
                'file': session_file,
                'knowledge_count': len(training_result.get('knowledge_items', [])),
                'categories': training_result.get('categories_covered', []),
                'timestamp': datetime.now().isoformat()
            })
            index['total_knowledge_items'] += len(training_result.get('knowledge_items', []))
            index['last_updated'] = datetime.now().isoformat()

            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(index, f, indent=2)

            print(f"💾 Saved {len(training_result.get('knowledge_items', []))} knowledge items to persistent storage")

        except Exception as e:
            print(f"❌ Error saving training knowledge: {e}")

    async def update_jarvis_capabilities(self, training_result: Dict):
        """Update Jarvis's code with new capabilities based on training results."""
        try:
            print("🔧 Updating Jarvis capabilities based on training results...")

            # Analyze what was learned
            categories = training_result.get('categories_covered', [])
            knowledge_items = training_result.get('knowledge_items_added', 0)

            # Generate capability improvements
            improvements = []

            if 'programming' in categories or 'coding' in categories:
                improvements.append({
                    'type': 'response_enhancement',
                    'area': 'programming',
                    'description': 'Enhanced programming knowledge and code generation capabilities'
                })

            if 'science' in categories or 'technology' in categories:
                improvements.append({
                    'type': 'knowledge_expansion',
                    'area': 'technical',
                    'description': 'Expanded technical knowledge base for better explanations'
                })

            if knowledge_items > 50:  # Significant learning
                improvements.append({
                    'type': 'conversation_improvement',
                    'area': 'general',
                    'description': 'Enhanced conversational abilities with broader knowledge'
                })

            # Apply improvements to conversation system
            await self.apply_capability_improvements(improvements)

            if self.gui_callback:
                self.gui_callback(f"🚀 Jarvis: Updated {len(improvements)} core capabilities based on training!")

        except Exception as e:
            print(f"❌ Error updating capabilities: {e}")

    async def apply_capability_improvements(self, improvements: List[Dict]):
        """Apply capability improvements to Jarvis's systems."""
        try:
            import json
            import os

            # Save improvements to capability enhancement file
            improvements_file = "jarvis_knowledge_persistence/capability_improvements.json"

            if os.path.exists(improvements_file):
                with open(improvements_file, 'r', encoding='utf-8') as f:
                    existing_improvements = json.load(f)
            else:
                existing_improvements = {'improvements': [], 'last_updated': ''}

            # Add new improvements
            for improvement in improvements:
                improvement['applied_at'] = datetime.now().isoformat()
                improvement['status'] = 'active'
                existing_improvements['improvements'].append(improvement)

            existing_improvements['last_updated'] = datetime.now().isoformat()

            # Save updated improvements
            with open(improvements_file, 'w', encoding='utf-8') as f:
                json.dump(existing_improvements, f, indent=2)

            # Update training metrics to reflect improvements
            self.training_metrics['response_quality_score'] = min(100,
                self.training_metrics['response_quality_score'] + (len(improvements) * 2))

            print(f"✅ Applied {len(improvements)} capability improvements")

        except Exception as e:
            print(f"❌ Error applying improvements: {e}")

    def stop_training(self) -> bool:
        """Stop current training session."""
        if self.training_active and self.current_session:
            # Create stop signal
            with open("stop_training.signal", "w") as f:
                f.write(f"STOP_TRAINING_{datetime.now().isoformat()}")
            
            self.training_active = False
            
            if self.gui_callback:
                self.gui_callback("🛑 Jarvis: Training session stopped by user request.")
            
            return True
        
        return False
    
    def get_feedback_summary(self) -> Dict:
        """Get summary of received feedback."""
        if not self.feedback_db:
            return {'total_feedback': 0, 'feedback_types': {}}
        
        try:
            cursor = self.feedback_db.cursor()
            
            # Total feedback count
            cursor.execute('SELECT COUNT(*) FROM user_feedback')
            total_feedback = cursor.fetchone()[0]
            
            # Feedback by type
            cursor.execute('''
                SELECT feedback_type, COUNT(*) 
                FROM user_feedback 
                GROUP BY feedback_type
            ''')
            feedback_types = dict(cursor.fetchall())
            
            # Recent improvements
            cursor.execute('''
                SELECT COUNT(*) FROM response_improvements 
                WHERE timestamp > datetime('now', '-7 days')
            ''')
            recent_improvements = cursor.fetchone()[0]
            
            return {
                'total_feedback': total_feedback,
                'feedback_types': feedback_types,
                'recent_improvements': recent_improvements,
                'quality_score': self.training_metrics['response_quality_score']
            }
            
        except Exception as e:
            print(f"⚠️ Error getting feedback summary: {e}")
            return {'total_feedback': 0, 'feedback_types': {}}
    
    def get_available_training_types(self) -> List[Dict]:
        """Get available training types with descriptions."""
        return [
            {
                'type': 'comprehensive',
                'name': 'Comprehensive Training',
                'description': 'Deep learning across all knowledge categories',
                'recommended_duration': '2-24 hours',
                'intensity': 'High'
            },
            {
                'type': 'focused',
                'name': 'Focused Training',
                'description': 'Targeted learning on specific topics',
                'recommended_duration': '30 minutes - 4 hours',
                'intensity': 'Medium'
            },
            {
                'type': 'rapid',
                'name': 'Rapid Training',
                'description': 'Quick knowledge updates and refreshers',
                'recommended_duration': '10-60 minutes',
                'intensity': 'Low'
            },
            {
                'type': 'feedback_focused',
                'name': 'Feedback-Focused Training',
                'description': 'Training based on user feedback and improvements',
                'recommended_duration': '20 minutes - 2 hours',
                'intensity': 'Medium'
            }
        ]
