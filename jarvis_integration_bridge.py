#!/usr/bin/env python3
"""
🌉 JARVIS INTEGRATION BRIDGE
Connects web app to main Jarvis GUI for seamless voice/text integration
"""

import socket
import json
import threading
import time
import subprocess
import os
import sys
from datetime import datetime

class JarvisIntegrationBridge:
    """Bridge between web app and main Jarvis GUI."""
    
    def __init__(self):
        self.bridge_port = 9999
        self.main_gui_port = 9998
        self.web_app_port = 8080
        self.running = True
        self.connections = {}
        
    def start_bridge_server(self):
        """Start the integration bridge server."""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', self.bridge_port))
            self.server_socket.listen(5)
            
            print(f"🌉 Integration bridge listening on port {self.bridge_port}")
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    print(f"🔗 New connection from {address}")
                    
                    # Handle connection in separate thread
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, address),
                        daemon=True
                    )
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ Bridge server error: {e}")
                        
        except Exception as e:
            print(f"❌ Failed to start bridge server: {e}")
    
    def handle_client(self, client_socket, address):
        """Handle individual client connections."""
        try:
            while self.running:
                data = client_socket.recv(4096)
                if not data:
                    break
                    
                try:
                    message = json.loads(data.decode('utf-8'))
                    response = self.process_bridge_message(message)
                    
                    if response:
                        client_socket.send(json.dumps(response).encode('utf-8'))
                        
                except json.JSONDecodeError:
                    print(f"❌ Invalid JSON from {address}")
                except Exception as e:
                    print(f"❌ Error handling client {address}: {e}")
                    
        except Exception as e:
            print(f"❌ Client handler error: {e}")
        finally:
            client_socket.close()
            print(f"🔌 Connection closed: {address}")
    
    def process_bridge_message(self, message):
        """Process messages between web app and main GUI."""
        try:
            msg_type = message.get('type')
            
            if msg_type == 'web_to_gui':
                # Forward web app message to main GUI
                return self.forward_to_main_gui(message)
                
            elif msg_type == 'gui_to_web':
                # Forward main GUI response to web app
                return self.forward_to_web_app(message)
                
            elif msg_type == 'voice_command':
                # Handle voice command from web app
                return self.handle_voice_command(message)
                
            elif msg_type == 'status_check':
                # Return bridge status
                return {
                    'type': 'status_response',
                    'bridge_active': True,
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            print(f"❌ Error processing bridge message: {e}")
            return {'type': 'error', 'message': str(e)}
    
    def forward_to_main_gui(self, message):
        """Forward message to main Jarvis GUI."""
        try:
            # Try to connect to main GUI
            gui_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            gui_socket.settimeout(5)
            gui_socket.connect(('localhost', self.main_gui_port))
            
            # Send message
            gui_socket.send(json.dumps(message).encode('utf-8'))
            
            # Get response
            response_data = gui_socket.recv(4096)
            response = json.loads(response_data.decode('utf-8'))
            
            gui_socket.close()
            return response
            
        except Exception as e:
            print(f"❌ Error forwarding to main GUI: {e}")
            return {
                'type': 'error',
                'message': 'Main GUI not available',
                'fallback_response': self.generate_fallback_response(message.get('content', ''))
            }
    
    def forward_to_web_app(self, message):
        """Forward message to web app."""
        try:
            # Implementation for forwarding to web app
            # This would be used if main GUI needs to send something to web app
            return {'type': 'forwarded', 'success': True}
            
        except Exception as e:
            print(f"❌ Error forwarding to web app: {e}")
            return {'type': 'error', 'message': str(e)}
    
    def handle_voice_command(self, message):
        """Handle voice command from web app."""
        try:
            voice_text = message.get('voice_text', '')
            
            # Process voice command through main GUI if available
            gui_message = {
                'type': 'voice_input',
                'content': voice_text,
                'source': 'web_app',
                'timestamp': datetime.now().isoformat()
            }
            
            return self.forward_to_main_gui(gui_message)
            
        except Exception as e:
            print(f"❌ Error handling voice command: {e}")
            return {'type': 'error', 'message': str(e)}
    
    def generate_fallback_response(self, message):
        """Generate fallback response when main GUI is not available."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['hello', 'hi', 'hey']):
            return "🤖 Jarvis: Good day, sir. Main systems are initializing."
        elif 'train' in message_lower:
            return "🎓 Jarvis: Training system is connecting to main interface."
        elif any(word in message_lower for word in ['status', 'progress']):
            return "📊 Jarvis: System status: Web interface active, connecting to main systems."
        else:
            return f"🤖 Jarvis: Processing '{message}' - Main systems will respond shortly."
    
    def start(self):
        """Start the integration bridge."""
        print("🌉 Starting Jarvis Integration Bridge...")
        
        # Start bridge server in separate thread
        bridge_thread = threading.Thread(target=self.start_bridge_server, daemon=True)
        bridge_thread.start()
        
        print("✅ Integration bridge started successfully")
        return True
    
    def stop(self):
        """Stop the integration bridge."""
        print("🛑 Stopping integration bridge...")
        self.running = False
        
        if hasattr(self, 'server_socket'):
            self.server_socket.close()
        
        print("✅ Integration bridge stopped")

def main():
    """Main function for testing the bridge."""
    bridge = JarvisIntegrationBridge()
    
    try:
        bridge.start()
        
        print("🌉 Integration Bridge running...")
        print("🔗 Waiting for connections...")
        print("🛑 Press Ctrl+C to stop")
        
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n👋 Bridge interrupted by user")
        bridge.stop()
    except Exception as e:
        print(f"❌ Bridge error: {e}")
        bridge.stop()

if __name__ == "__main__":
    main()
