#!/usr/bin/env python3
"""
🌍 Global Access Setup for Jarvis Web App
Creates a public URL accessible from anywhere in the world
"""

import subprocess
import time
import requests
import webbrowser
import os
import json

def check_jarvis_running():
    """Check if Jarvis Web App is running."""
    try:
        response = requests.get('http://localhost:8080', timeout=3)
        if response.status_code == 200:
            print("✅ Jarvis Web App is running on port 8080")
            return True
    except:
        pass
    
    try:
        response = requests.get('http://localhost:5000', timeout=3)
        if response.status_code == 200:
            print("✅ Jarvis Web App is running on port 5000")
            return True
    except:
        pass
    
    print("❌ Jarvis Web App is NOT running!")
    print("🔧 Start it first with: python jarvis_web_app.py")
    return False

def check_ngrok():
    """Check if ngrok is available."""
    # Check for ngrok.exe in current directory
    if os.path.exists('ngrok.exe'):
        try:
            result = subprocess.run(['./ngrok.exe', 'version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ ngrok found: {result.stdout.strip()}")
                return './ngrok.exe'
        except:
            pass
    
    # Check for ngrok in PATH
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ ngrok found: {result.stdout.strip()}")
            return 'ngrok'
    except:
        pass
    
    return None

def download_instructions():
    """Show download instructions."""
    print("📥 NGROK DOWNLOAD REQUIRED")
    print("=" * 30)
    print("1. Download ngrok from: https://ngrok.com/download")
    print("2. Choose 'Windows (64-bit)'")
    print("3. Extract the zip file")
    print("4. Copy 'ngrok.exe' to this folder")
    print("5. Run this script again")
    print()
    print("📁 Put ngrok.exe in this folder:")
    print(f"   {os.getcwd()}")

def start_ngrok_tunnel(ngrok_cmd, port=8080):
    """Start ngrok tunnel."""
    print(f"🚀 Creating global tunnel for port {port}...")
    print("⚠️ This creates a PUBLIC URL - accessible from anywhere!")
    print()
    
    try:
        # Start ngrok process
        process = subprocess.Popen([
            ngrok_cmd, 'http', str(port), '--log=stdout'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("🔄 Starting tunnel...")
        time.sleep(5)  # Wait for ngrok to initialize
        
        # Get the public URL from ngrok API
        try:
            response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
            data = response.json()
            
            if data.get('tunnels') and len(data['tunnels']) > 0:
                tunnel = data['tunnels'][0]
                public_url = tunnel['public_url']
                
                print()
                print("🎉 SUCCESS! GLOBAL ACCESS CREATED!")
                print("=" * 50)
                print(f"🌍 PUBLIC URL: {public_url}")
                print("=" * 50)
                print()
                print("📱 ACCESS FROM ANYWHERE:")
                print(f"   iPhone: {public_url}")
                print(f"   Any device: {public_url}")
                print(f"   Any network: {public_url}")
                print()
                print("🌐 This URL works from:")
                print("   ✅ Any WiFi network")
                print("   ✅ Cellular data (4G/5G)")
                print("   ✅ Public WiFi (coffee shops, airports)")
                print("   ✅ Work/office networks")
                print("   ✅ Any country/location")
                print()
                print("⚠️ SECURITY NOTE:")
                print("   - This URL is PUBLIC")
                print("   - Anyone with the link can access your Jarvis")
                print("   - URL stops working when you close this")
                print()
                print("🛑 Press Ctrl+C to stop the tunnel")
                
                # Try to open in browser
                try:
                    webbrowser.open(public_url)
                    print("🌐 Opening in browser...")
                except:
                    pass
                
                return process, public_url
            else:
                print("❌ No tunnels found in ngrok response")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Cannot connect to ngrok API: {e}")
            print("🔧 ngrok might still be starting...")
            print("💡 Try opening http://localhost:4040 in your browser")
            
    except Exception as e:
        print(f"❌ Error starting ngrok: {e}")
    
    return None, None

def main():
    """Main setup function."""
    print("🌍 GLOBAL ACCESS SETUP FOR JARVIS WEB APP")
    print("=" * 50)
    print("Creates a public URL accessible from anywhere in the world!")
    print()
    
    # Check if Jarvis is running
    if not check_jarvis_running():
        return
    
    # Check for ngrok
    ngrok_cmd = check_ngrok()
    if not ngrok_cmd:
        download_instructions()
        return
    
    # Determine port
    port = 8080
    try:
        requests.get('http://localhost:8080', timeout=1)
    except:
        try:
            requests.get('http://localhost:5000', timeout=1)
            port = 5000
        except:
            print("❌ Cannot determine Jarvis port")
            return
    
    print(f"🔍 Detected Jarvis running on port {port}")
    print()
    
    # Start tunnel
    process, public_url = start_ngrok_tunnel(ngrok_cmd, port)
    
    if process and public_url:
        try:
            print("\n⏳ Tunnel is active... (Press Ctrl+C to stop)")
            print(f"📱 Use this URL on your iPhone: {public_url}")
            print()
            
            # Keep the tunnel running
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping global tunnel...")
            process.terminate()
            print("👋 Tunnel stopped - public URL no longer works")
            print("🔒 Your Jarvis is now private again")
    else:
        print("❌ Failed to create tunnel")
        print()
        print("🔧 MANUAL SETUP:")
        print("1. Open Command Prompt in this folder")
        print("2. Run: ngrok http 8080")
        print("3. Copy the 'Forwarding' URL")
        print("4. Use that URL on your iPhone")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("🔧 Try manual setup with: ngrok http 8080")
