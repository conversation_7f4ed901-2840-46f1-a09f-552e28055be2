#!/usr/bin/env python3
"""
🌌 J.A.R.V.I.S Web App - Quantum Consciousness Interface
Responsive web version optimized for iPhone 16+ and Tailscale
"""

from flask import Flask, render_template, request, jsonify, session
from flask_socketio import Socket<PERSON>, emit
import json
import os
import time
from datetime import datetime
import threading
import asyncio
import socket
import subprocess
import sys

# Import existing Jarvis components
try:
    from enhanced_training_system import EnhancedTrainingSystem
    from ultra_command_system import UltraCommandSystem
    from advanced_jarvis_brain import AdvancedJarvisBrain
    from user_manager import UserManager
    from real_file_creator import RealFileCreator
    from web_intelligence import WebIntelligenceEngine
except ImportError as e:
    print(f"⚠️ Some Jarvis components not available: {e}")

app = Flask(__name__)
app.config['SECRET_KEY'] = 'jarvis_quantum_consciousness_2075'
socketio = SocketIO(app, cors_allowed_origins="*")

class JarvisWebApp:
    def __init__(self):
        """Initialize Jarvis Web App with all systems."""
        self.initialize_systems()
        self.setup_routes()
        
    def initialize_systems(self):
        """Initialize all Jarvis systems."""
        print("🚀 Initializing Jarvis Web App Systems...")
        
        # Initialize core systems
        try:
            self.enhanced_training = EnhancedTrainingSystem(gui_callback=self.emit_message)
            print("✅ Enhanced Training System initialized")
        except:
            self.enhanced_training = None
            print("⚠️ Enhanced Training System fallback")
            
        try:
            self.ultra_command_system = UltraCommandSystem()
            print("✅ Ultra Command System initialized")
        except:
            self.ultra_command_system = None
            print("⚠️ Ultra Command System fallback")
            
        try:
            self.advanced_brain = AdvancedJarvisBrain()
            print("✅ Advanced Jarvis Brain initialized")
        except:
            self.advanced_brain = None
            print("⚠️ Advanced Jarvis Brain fallback")
            
        try:
            self.user_manager = UserManager()
            print("✅ User Manager initialized")
        except:
            self.user_manager = None
            print("⚠️ User Manager fallback")
            
        try:
            self.file_creator = RealFileCreator()
            print("✅ File Creator initialized")
        except:
            self.file_creator = None
            print("⚠️ File Creator fallback")
            
        try:
            self.web_intelligence = WebIntelligenceEngine()
            print("✅ Web Intelligence initialized")
        except:
            self.web_intelligence = None
            print("⚠️ Web Intelligence fallback")
            
        # App state
        self.active_sessions = {}
        self.chat_history = []
        self.current_theme = "dark_professional"
        
        print("🎉 Jarvis Web App Systems Initialized!")

    def emit_message(self, message):
        """Emit message to all connected clients."""
        socketio.emit('chat_message', {'message': message, 'timestamp': datetime.now().isoformat()})

    def check_main_gui_connection(self):
        """Check if main GUI is available."""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', 9998))
            sock.close()
            return result == 0
        except:
            return False

    def send_to_main_gui(self, message, msg_type='text'):
        """Send message to main Jarvis GUI."""
        try:
            import socket
            import json

            # Create message for main GUI
            gui_message = {
                'type': 'web_app_message',
                'content': message,
                'message_type': msg_type,
                'source': 'web_app',
                'timestamp': datetime.now().isoformat()
            }

            # Try to connect to main GUI
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect(('localhost', 9998))

            # Send message
            sock.send(json.dumps(gui_message).encode('utf-8'))

            # Get response
            response_data = sock.recv(4096)
            response = json.loads(response_data.decode('utf-8'))

            sock.close()

            return response.get('response', 'Main GUI processed your request.')

        except Exception as e:
            print(f"❌ Error connecting to main GUI: {e}")
            return None
        
    def setup_routes(self):
        """Setup Flask routes."""
        
        @app.route('/')
        def index():
            """Main Jarvis interface."""
            return render_template('jarvis.html', 
                                 current_theme=self.current_theme,
                                 chat_history=self.chat_history[-50:])  # Last 50 messages
        
        @app.route('/api/send_message', methods=['POST'])
        def send_message():
            """Process user message."""
            data = request.get_json()
            message = data.get('message', '').strip()
            
            if not message:
                return jsonify({'error': 'Empty message'}), 400
                
            # Add user message to history
            user_msg = {
                'type': 'user',
                'message': message,
                'timestamp': datetime.now().isoformat()
            }
            self.chat_history.append(user_msg)
            
            # Process message and get response
            response = self.process_message(message)
            
            # Add Jarvis response to history
            jarvis_msg = {
                'type': 'jarvis',
                'message': response,
                'timestamp': datetime.now().isoformat()
            }
            self.chat_history.append(jarvis_msg)
            
            # Emit to all clients
            socketio.emit('new_message', user_msg)
            socketio.emit('new_message', jarvis_msg)
            
            return jsonify({'response': response})
            
        @app.route('/api/training/start', methods=['POST'])
        def start_training():
            """Start training session."""
            data = request.get_json()
            duration = data.get('duration', 1.0)
            topic = data.get('topic', 'general knowledge')
            training_type = data.get('type', 'comprehensive')
            
            if self.enhanced_training:
                result = self.enhanced_training.start_custom_training(
                    duration_hours=duration,
                    training_type=training_type,
                    custom_topics=[topic] if topic != 'general knowledge' else None
                )
                return jsonify(result)
            else:
                return jsonify({'success': False, 'message': 'Training system not available'})
                
        @app.route('/api/training/progress')
        def get_training_progress():
            """Get training progress."""
            if self.enhanced_training:
                progress = self.enhanced_training.get_training_progress()
                return jsonify(progress)
            else:
                return jsonify({'training_active': False, 'active_sessions': {}, 'metrics': {}})
                
        @app.route('/api/training/sessions')
        def get_all_sessions():
            """Get all training sessions."""
            if self.enhanced_training:
                sessions = self.enhanced_training.get_all_sessions()
                return jsonify(sessions)
            else:
                return jsonify({'active_sessions': {}, 'recent_sessions': []})
                
        @app.route('/api/training/session/<session_id>/pause', methods=['POST'])
        def pause_session(session_id):
            """Pause training session."""
            if self.enhanced_training:
                result = self.enhanced_training.pause_session(session_id)
                return jsonify(result)
            else:
                return jsonify({'success': False, 'message': 'Training system not available'})
                
        @app.route('/api/training/session/<session_id>/stop', methods=['POST'])
        def stop_session(session_id):
            """Stop training session."""
            if self.enhanced_training:
                result = self.enhanced_training.stop_session(session_id)
                return jsonify(result)
            else:
                return jsonify({'success': False, 'message': 'Training system not available'})
                
        @app.route('/api/theme/<theme_name>', methods=['POST'])
        def change_theme(theme_name):
            """Change interface theme."""
            valid_themes = ['dark_professional', 'neon_cyber', 'matrix_green', 'electric_blue']
            if theme_name in valid_themes:
                self.current_theme = theme_name
                socketio.emit('theme_changed', {'theme': theme_name})
                return jsonify({'success': True, 'theme': theme_name})
            else:
                return jsonify({'success': False, 'message': 'Invalid theme'}), 400
                
        @app.route('/api/system/status')
        def get_system_status():
            """Get system status."""
            status = {
                'enhanced_training': self.enhanced_training is not None,
                'ultra_command_system': self.ultra_command_system is not None,
                'advanced_brain': self.advanced_brain is not None,
                'user_manager': self.user_manager is not None,
                'file_creator': self.file_creator is not None,
                'web_intelligence': self.web_intelligence is not None,
                'active_sessions': len(self.active_sessions),
                'chat_messages': len(self.chat_history),
                'current_theme': self.current_theme,
                'main_gui_connected': self.check_main_gui_connection(),
                'timestamp': datetime.now().isoformat()
            }
            return jsonify(status)

        @app.route('/api/voice/process', methods=['POST'])
        def process_voice():
            """Process voice input from web app."""
            data = request.get_json()
            voice_text = data.get('voice_text', '').strip()

            if not voice_text:
                return jsonify({'error': 'Empty voice input'}), 400

            # Try to send to main GUI first
            main_response = self.send_to_main_gui(voice_text, 'voice')

            if main_response:
                return jsonify({
                    'response': main_response,
                    'source': 'main_gui',
                    'voice_enabled': True
                })
            else:
                # Fallback to web app processing
                response = self.process_message(voice_text)
                return jsonify({
                    'response': response,
                    'source': 'web_app',
                    'voice_enabled': False
                })

        @app.route('/api/main_gui/send', methods=['POST'])
        def send_to_main():
            """Send message directly to main GUI."""
            data = request.get_json()
            message = data.get('message', '').strip()
            msg_type = data.get('type', 'text')

            if not message:
                return jsonify({'error': 'Empty message'}), 400

            response = self.send_to_main_gui(message, msg_type)

            if response:
                return jsonify({
                    'success': True,
                    'response': response,
                    'source': 'main_gui'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Main GUI not available',
                    'fallback_response': self.process_message(message)
                })
            
    def process_message(self, message):
        """Process user message and generate response."""
        try:
            # Use ultra command system if available
            if self.ultra_command_system:
                command_match = self.ultra_command_system.analyze_command(message)
                
                # Handle training commands
                if 'train' in message.lower() and 'for' in message.lower():
                    return self.handle_training_command(message)
                    
                # Handle progress requests
                if any(word in message.lower() for word in ['progress', 'status', 'training']):
                    return self.handle_progress_request()
                    
            # Use advanced brain if available
            if self.advanced_brain:
                response = self.advanced_brain.get_enhanced_response(message)
                return f"🤖 Jarvis: {response}"
                
            # Fallback response
            return self.generate_fallback_response(message)
            
        except Exception as e:
            print(f"❌ Error processing message: {e}")
            return "🤖 Jarvis: I apologize, sir. I'm experiencing a temporary processing error. Please try again."
            
    def handle_training_command(self, message):
        """Handle training commands."""
        # Extract duration and topic from message
        import re
        duration_match = re.search(r'(\d+(?:\.\d+)?)\s*(minute|hour|day)s?', message.lower())
        
        if duration_match:
            duration = float(duration_match.group(1))
            unit = duration_match.group(2)
            
            # Convert to hours
            if unit.startswith('minute'):
                duration_hours = duration / 60
            elif unit.startswith('day'):
                duration_hours = duration * 24
            else:
                duration_hours = duration
                
            # Extract topic
            topic = "general knowledge"
            if "about" in message.lower():
                topic_part = message.lower().split("about")[1].strip()
                if topic_part:
                    topic = topic_part
                    
            # Start training
            if self.enhanced_training:
                result = self.enhanced_training.start_custom_training(
                    duration_hours=duration_hours,
                    training_type='comprehensive',
                    custom_topics=[topic] if topic != 'general knowledge' else None
                )
                if result.get('success'):
                    return f"🚀 Jarvis: Starting {duration} {unit} training session on {topic}. Training initiated successfully!"
                else:
                    return f"❌ Jarvis: Training failed: {result.get('message', 'Unknown error')}"
            else:
                return "⚠️ Jarvis: Training system is not available at the moment."
        else:
            return "🎓 Jarvis: Please specify a duration, like 'train for 2 hours' or 'training for 30 minutes'."
            
    def handle_progress_request(self):
        """Handle progress requests."""
        if self.enhanced_training:
            progress = self.enhanced_training.get_training_progress()
            active_sessions = progress.get('active_sessions', {})
            
            if active_sessions:
                response = f"📊 Jarvis: Current training progress:\n"
                for session_id, session in active_sessions.items():
                    response += f"• {session_id}: {session.get('progress', 0):.1f}% complete\n"
                    response += f"  Knowledge gained: {session.get('knowledge_gained', 0)} items\n"
                return response
            else:
                return "💤 Jarvis: No active training sessions currently running."
        else:
            return "⚠️ Jarvis: Training system is not available for progress monitoring."
            
    def generate_fallback_response(self, message):
        """Generate fallback response."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['hello', 'hi', 'hey']):
            return "🤖 Jarvis: Good day, sir. How may I assist you today?"
        elif any(word in message_lower for word in ['help', 'what can you do']):
            return "🤖 Jarvis: I can help with training, file creation, web research, and system management. Try saying 'train for 1 hour about AI' or 'show training progress'."
        elif any(word in message_lower for word in ['thank', 'thanks']):
            return "🤖 Jarvis: You're most welcome, sir. Always at your service."
        else:
            return f"🤖 Jarvis: I understand you said '{message}'. How may I assist you further?"

# Initialize Jarvis Web App
jarvis_app = JarvisWebApp()

@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    print('🔗 Client connected')
    emit('connected', {'message': 'Connected to Jarvis Web Interface'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    print('🔌 Client disconnected')

if __name__ == '__main__':
    print("🌌 Starting J.A.R.V.I.S Web App...")
    print("📱 Optimized for iPhone 16+ and Tailscale")
    print("🚀 Tailscale Access: http://************:8080")
    print("🌐 Local Access: http://127.0.0.1:8080")
    
    # Run with Tailscale-friendly settings
    socketio.run(app, host='0.0.0.0', port=8080, debug=False, allow_unsafe_werkzeug=True)
