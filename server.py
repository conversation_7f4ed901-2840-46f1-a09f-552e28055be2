from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON>P<PERSON>x<PERSON>, Header
from pydantic import BaseModel
from typing import Optional, List, Dict
import asyncio
import llama
import resemble_ai_tts
from self_improvement import jarvis_self_improvement
from web_intelligence import jarvis_web_intelligence
from security_backup_integration import jarvis_security_backup
from self_optimizer import jarvis_self_optimizer
from real_file_creator import RealFileCreator
from project_manager import ProjectManager
import config
import speed_config
from datetime import datetime
import time

app = FastAPI()
llama_model = llama.LlamaModel()

# Initialize file creation systems
file_creator = RealFileCreator()
project_manager = ProjectManager()

# Global progress tracking for GUI updates
current_progress = {
    'task_type': 'idle',
    'percent': 0,
    'status': 'No active tasks',
    'details': '',
    'timestamp': None
}

async def broadcast_progress_update(task_type: str, percent: int, status: str, details: str):
    """Update global progress state for GUI polling."""
    global current_progress
    current_progress.update({
        'task_type': task_type,
        'percent': percent,
        'status': status,
        'details': details,
        'timestamp': datetime.now().isoformat()
    })

class CommandRequest(BaseModel):
    command: str

class ImprovementRequest(BaseModel):
    suggestion_id: str | None = None
    auto_approve: bool = False

class ErrorReportRequest(BaseModel):
    error_description: str
    expected_behavior: str = ""
    steps_to_reproduce: str = ""
    severity: str = "medium"  # low, medium, high, critical
    auto_fix: bool = True

class WebSearchRequest(BaseModel):
    query: str
    learn_from_results: bool = True
    max_results: int = 10

class URLLearningRequest(BaseModel):
    url: str

class YouTubeLearnRequest(BaseModel):
    video_url: str
    extract_transcript: bool = True

class KnowledgeSearchRequest(BaseModel):
    query: str
    max_results: int = 5

# Security & Backup Request Models
class LoginRequest(BaseModel):
    username: str
    password: str

class CreateUserRequest(BaseModel):
    username: str
    password: str
    email: str
    role: str = "user"
    full_name: str = ""

class BackupRequest(BaseModel):
    description: str | None = None
    tags: list = []

class RestoreRequest(BaseModel):
    backup_id: str
    restore_path: str | None = None

@app.get("/ready")
async def ready():
    """
    Endpoint to indicate that the server is ready to receive requests.
    """
    return {"status": "ready"}

def detect_input_type(text: str) -> str:
    """Detect if input is a command or conversation."""
    text_lower = text.lower().strip()

    # Check for conversation improvement commands first (highest priority)
    conversation_improvement_patterns = [
        'improve your conversations', 'improve your conversation skills', 'learn better conversations',
        'improve how you talk', 'get better at talking', 'learn conversation skills', 'improve communication',
        'learn to communicate better', 'improve your responses', 'learn better responses', 'study conversations',
        'research conversation techniques', 'learn from conversation experts', 'improve dialogue skills'
    ]

    # Check for self-optimization requests first (highest priority)
    optimization_phrases = [
        'make yourself faster', 'speed up', 'be faster', 'improve speed', 'optimize speed',
        'make responses faster', 'respond faster', 'be quicker', 'improve performance',
        'optimize yourself', 'make yourself better', 'improve efficiency', 'be more efficient',
        'use less memory', 'be more responsive', 'reduce lag', 'improve accuracy',
        'make your responses faster', 'speed up your responses', 'optimize your performance'
    ]

    if any(phrase in text_lower for phrase in optimization_phrases):
        return "self_optimization"

    # Check for self-improvement patterns with "your" referring to Jarvis
    jarvis_self_improvement_patterns = [
        'improve your', 'learn better', 'get better at', 'enhance your', 'upgrade your',
        'optimize your', 'develop your', 'strengthen your', 'refine your', 'perfect your',
        'advance your', 'boost your', 'increase your', 'expand your', 'grow your'
    ]

    # If it contains self-improvement language, it's likely about Jarvis improving himself
    if any(pattern in text_lower for pattern in jarvis_self_improvement_patterns):
        # Check if it's about conversation skills
        if any(conv_word in text_lower for conv_word in ['conversation', 'talk', 'speak', 'communication', 'dialogue', 'chat']):
            return "conversation_improvement"
        # Check if it's about other self-improvement areas
        elif any(skill_word in text_lower for skill_word in ['pronunciation', 'voice', 'speech', 'audio', 'sound', 'performance', 'speed', 'memory', 'intelligence', 'skills', 'abilities', 'capabilities']):
            return "self_improvement"

    if any(pattern in text_lower for pattern in conversation_improvement_patterns):
        return "conversation_improvement"

    # Check for web learning commands (high priority)
    web_learning_patterns = [
        'learn from', 'analyze', 'study', 'research', 'search for', 'search', 'find information about',
        'look up', 'investigate', 'explore', 'examine', 'learn about'
    ]
    if any(pattern in text_lower for pattern in web_learning_patterns):
        return "command"

    # Check for URL patterns
    if any(url_start in text_lower for url_start in ['http://', 'https://', 'www.']):
        return "command"

    # Check for direct command indicators
    command_indicators = ['jarvis', 'execute', 'run', 'open', 'close', 'start', 'stop']
    if any(indicator in text_lower for indicator in command_indicators):
        return "command"

    # Check for command keywords by category
    command_keywords = {
        'system': ['open', 'launch', 'start', 'run', 'execute', 'activate', 'enable', 'disable', 'stop', 'close', 'shutdown'],
        'web_intelligence': ['search', 'find', 'look up', 'research', 'learn', 'analyze', 'study', 'investigate', 'explore'],
        'action': ['create', 'make', 'build', 'generate', 'write', 'send', 'calculate'],
        'time': ['time', 'date', 'today', 'tomorrow', 'yesterday', 'now', 'current'],
        'weather': ['weather', 'temperature', 'forecast', 'rain', 'sunny', 'cloudy'],
        'control': ['volume', 'brightness', 'settings', 'configuration', 'preferences']
    }

    for _category, keywords in command_keywords.items():
        if any(keyword in text_lower for keyword in keywords):
            return "command"

    # Check for question words (likely conversation) - but only if not already caught above
    question_words = ['what', 'how', 'why', 'when', 'where', 'who', 'can you', 'do you', 'are you', 'will you']
    if any(word in text_lower for word in question_words):
        # Double-check if it's actually a search command disguised as a question
        if any(search_word in text_lower for search_word in ['search', 'find', 'look up', 'research']):
            return "command"
        return "conversation"

    # Check for conversational patterns
    conversational_patterns = [
        'i think', 'i feel', 'i like', 'i want', 'i need', 'tell me about',
        'what do you think', 'how do you feel', 'in my opinion',
        'that\'s interesting', 'really?', 'wow', 'cool', 'nice'
    ]
    if any(pattern in text_lower for pattern in conversational_patterns):
        return "conversation"

    # Default to conversation for natural language
    return "conversation"

def _extract_key_terms(user_input: str) -> str:
    """Extract key terms from user input for better knowledge matching."""
    import re

    # Remove question words and common words
    question_words = ['what', 'how', 'why', 'when', 'where', 'who', 'can', 'do', 'are', 'is', 'tell', 'me', 'about']
    common_words = ['the', 'and', 'for', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'way', 'some', 'good', 'best', 'more', 'most', 'i', 'my', 'in', 'to', 'a', 'an']

    # Extract words and clean them
    words = re.findall(r'\b[a-zA-Z]{3,}\b', user_input.lower())

    # Filter out question words and common words
    key_terms = [word for word in words if word not in question_words and word not in common_words and len(word) > 2]

    # Return the most relevant terms (up to 3)
    if key_terms:
        return ' '.join(key_terms[:3])
    else:
        # Fallback to original input if no key terms found
        return user_input

async def create_conversation_prompt(user_input: str) -> str:
    """Create a conversational prompt for the Llama model with knowledge integration."""

    # Retrieve relevant knowledge for the conversation
    try:
        from web_intelligence import jarvis_web_intelligence

        # Extract key terms from user input for better knowledge matching
        search_terms = _extract_key_terms(user_input)

        # Get relevant knowledge for the user's input
        knowledge_result = await jarvis_web_intelligence.get_knowledge_for_query(
            query=search_terms,
            max_results=3
        )

        knowledge_context = ""
        if knowledge_result.get('success') and knowledge_result.get('knowledge_items'):
            knowledge_items = knowledge_result['knowledge_items']

            # Format knowledge for context
            knowledge_context = "\n\nRelevant knowledge from my training:\n"
            for i, item in enumerate(knowledge_items[:3], 1):
                knowledge_context += f"{i}. {item.get('title', 'Knowledge')}: {item.get('summary', item.get('content', ''))[:200]}...\n"

            knowledge_context += "\nUse this knowledge to provide a more informed and helpful response.\n"

    except Exception as e:
        print(f"⚠️ Error retrieving knowledge for conversation: {e}")
        knowledge_context = ""

    return f"""You are Jarvis, an AI assistant inspired by Iron Man's AI. You are having a natural conversation with the user. Be helpful, friendly, and engaging. Use your knowledge to provide informed responses.{knowledge_context}

User: {user_input}
Jarvis:"""

def create_command_prompt(user_input: str) -> str:
    """Create a command-focused prompt for the Llama model."""
    return f"""You are Jarvis, an AI assistant. The user has given you a command or task. Provide a helpful and direct response to execute or acknowledge the command.

Command: {user_input}
Jarvis:"""

async def process_web_intelligence_command(user_input: str) -> str | None:
    """Process web intelligence commands and return appropriate response."""
    user_input_lower = user_input.lower().strip()

    try:
        # URL Learning Commands
        if any(pattern in user_input_lower for pattern in ['learn from', 'analyze']) and any(url_start in user_input for url_start in ['http://', 'https://', 'www.']):
            # Extract URL from input
            import re
            url_pattern = r'https?://[^\s]+|www\.[^\s]+'
            urls = re.findall(url_pattern, user_input)

            if urls:
                url = urls[0]

                # Store progress updates for real-time tracking
                progress_updates = []

                async def progress_callback(percent, status, details):
                    progress_updates.append({
                        'percent': percent,
                        'status': status,
                        'details': details,
                        'timestamp': datetime.now().isoformat()
                    })
                    # Broadcast progress to connected clients (if any WebSocket connections exist)
                    await broadcast_progress_update("web_learning", percent, status, details)

                # Call web intelligence to learn from URL with progress tracking
                print(f"🔧 Calling learn_from_url with progress callback")
                result = await jarvis_web_intelligence.learn_from_url(url, progress_callback=progress_callback)
                print(f"🔧 Web intelligence result: {result}")

                # Reset progress to idle when complete
                await broadcast_progress_update("idle", 0, "No active tasks", "")

                if result.get('success'):
                    articles_learned = result.get('articles_learned', 0)
                    knowledge_items = result.get('knowledge_items_added', 0)
                    response_text = f"I successfully learned from {url}. I processed {articles_learned} articles and added {knowledge_items} knowledge items to my database."
                    print(f"🔧 Success response: {response_text}")
                    return response_text
                else:
                    error_msg = str(result.get('error', 'Unknown error'))
                    response_text = f"I encountered an issue while learning from {url}: {error_msg}"
                    print(f"🔧 Error response: {response_text}")
                    return response_text
            else:
                return "I couldn't find a valid URL in your request. Please provide a URL starting with http:// or https://"

        # Web Search Commands
        elif any(pattern in user_input_lower for pattern in ['search for', 'search', 'find information about', 'look up', 'research']):
            # Extract search query
            search_terms = ['search for', 'search', 'find information about', 'look up', 'research', 'find']
            query = user_input_lower

            for term in search_terms:
                if term in query:
                    query = query.replace(term, '').strip()
                    break

            if query:
                # Call web intelligence to search and learn
                result = await jarvis_web_intelligence.search_and_learn(query, learn_from_results=True)

                if result.get('success'):
                    total_results = result.get('total_results', 0)
                    articles_processed = result.get('learning_summary', {}).get('articles_processed', 0)
                    knowledge_items = result.get('learning_summary', {}).get('knowledge_items_added', 0)

                    return f"I searched for '{query}' and found {total_results} results. I successfully learned from {articles_processed} articles and added {knowledge_items} knowledge items to my database. This information is now available for future questions and assistance."
                else:
                    return f"I encountered an issue while searching for '{query}': {result.get('error', 'Unknown error')}"
            else:
                return "Please provide a search query. For example: 'search for artificial intelligence'"

        else:
            return None  # Not a web intelligence command

    except Exception as e:
        print(f"Error in web intelligence command processing: {e}")
        return f"I encountered an error while processing your web intelligence request: {str(e)}"

@app.post("/process_command")
async def process_command(request: CommandRequest):
    """
    Processes a command or conversation with optimized speed.
    """
    user_input = request.command
    print(f"🚀 Processing: {user_input}")

    # Quick input type detection
    input_type = detect_input_type(user_input)
    print(f"📝 Type: {input_type}")

    # Check for self-optimization requests first
    if input_type == "self_optimization":
        try:
            print(f"🔧 Processing self-optimization request: {user_input}")

            # Understand the optimization request
            optimization_request = await jarvis_self_optimizer.understand_optimization_request(user_input)
            print(f"🔧 Optimization request understood: {optimization_request}")

            if optimization_request['confidence'] > 0:
                # Analyze current performance
                print("📊 Analyzing current performance...")
                await jarvis_self_optimizer.analyze_current_performance()

                # Implement optimizations
                print("⚡ Implementing optimizations...")
                results = await jarvis_self_optimizer.implement_optimizations(optimization_request)

                # Generate report
                report = jarvis_self_optimizer.generate_optimization_report(results)
                print(f"📋 Optimization report: {report}")

                # Convert to speech
                audio_data = await resemble_ai_tts.text_to_speech(report)
                if audio_data:
                    return {"response": report, "audio": audio_data}
                else:
                    return {"response": report, "audio": None}
            else:
                fallback_response = "I understand you want me to optimize myself, but I need more specific guidance. You can say things like 'make yourself faster', 'improve your performance', or 'use less memory'."
                audio_data = await resemble_ai_tts.text_to_speech(fallback_response)
                return {"response": fallback_response, "audio": audio_data}

        except Exception as e:
            print(f"❌ Self-optimization error: {e}")
            error_response = f"I encountered an error while trying to optimize myself: {str(e)}. I'll continue working with my current configuration."
            audio_data = await resemble_ai_tts.text_to_speech(error_response)
            return {"response": error_response, "audio": audio_data}

    # Check for conversation improvement commands
    elif input_type == "conversation_improvement":
        try:
            # Return a response that will trigger the GUI conversation improvement system
            conversation_improvement_response = "I'm starting a comprehensive conversation improvement program. I'll research conversation techniques, study expert advice, and analyze successful dialogue patterns to enhance my communication skills. You can monitor my progress in the Task Monitor."

            # Convert to speech
            audio_data = await resemble_ai_tts.text_to_speech(conversation_improvement_response)
            if audio_data:
                return {"response": conversation_improvement_response, "audio": audio_data}
            else:
                return {"response": conversation_improvement_response, "audio": None}
        except Exception as e:
            print(f"Conversation improvement error: {e}")
            error_response = f"I encountered an error while starting the conversation improvement process: {str(e)}"
            return {"response": error_response, "audio": None}

    # Check for self-improvement commands
    elif input_type == "self_improvement":
        text_lower = user_input.lower().strip()

        try:
            # Determine what area to improve
            if any(word in text_lower for word in ['pronunciation', 'voice', 'speech']):
                improvement_response = "I understand you want me to improve my pronunciation and speech quality. I'm starting a comprehensive voice improvement program where I'll research speech techniques, study phonetics, and analyze clear speech patterns to enhance my vocal delivery. You can monitor my progress in the Task Monitor."
            elif any(word in text_lower for word in ['performance', 'speed', 'efficiency']):
                improvement_response = "I'm initiating a performance optimization program. I'll analyze my code for efficiency improvements, optimize my response times, and enhance my overall system performance. You can track my progress in the Task Monitor."
            elif any(word in text_lower for word in ['memory', 'remember', 'recall']):
                improvement_response = "I'm starting a memory enhancement program. I'll improve my ability to remember conversations, learn better information retention techniques, and optimize my memory systems. You can monitor my progress in the Task Monitor."
            elif any(word in text_lower for word in ['intelligence', 'smart', 'clever', 'thinking']):
                improvement_response = "I'm beginning an intelligence enhancement program. I'll study advanced reasoning techniques, learn problem-solving strategies, and improve my analytical capabilities. You can track my progress in the Task Monitor."
            else:
                improvement_response = "I understand you want me to improve myself. I'm starting a comprehensive self-improvement program to enhance my capabilities across multiple areas. You can monitor my progress in the Task Monitor."

            # Convert to speech
            audio_data = await resemble_ai_tts.text_to_speech(improvement_response)
            if audio_data:
                return {"response": improvement_response, "audio": audio_data}
            else:
                return {"response": improvement_response, "audio": None}

        except Exception as e:
            print(f"Self-improvement error: {e}")
            error_response = f"I encountered an error while starting the self-improvement process: {str(e)}"
            return {"response": error_response, "audio": None}

    # Check if this is a command
    elif input_type == "command":
        # Check for web intelligence commands
        web_intelligence_response = await process_web_intelligence_command(user_input)
        if web_intelligence_response:
            print(f"Web intelligence response: {web_intelligence_response}")

            # Convert to speech using Resemble AI
            try:
                audio_data = await resemble_ai_tts.text_to_speech(web_intelligence_response)
                if audio_data:
                    # Ensure audio data is properly encoded for JSON response
                    if isinstance(audio_data, bytes):
                        import base64
                        audio_data = base64.b64encode(audio_data).decode('utf-8')
                    return {"response": web_intelligence_response, "audio": audio_data}
                else:
                    return {"response": web_intelligence_response, "audio": None}
            except Exception as e:
                print(f"TTS Error: {e}")
                return {"response": web_intelligence_response, "audio": None}

    # Fast response generation with performance monitoring
    start_time = time.time()
    try:
        if input_type == "conversation":
            prompt = await create_conversation_prompt(user_input)
        else:
            prompt = create_command_prompt(user_input)

        # Generate response using optimized Llama model
        llama_response = llama_model.generate_response(prompt)

        # Calculate processing time
        processing_time = time.time() - start_time
        print(f"✅ Response generated: {len(llama_response)} chars in {processing_time:.2f}s")

        # Check if response time meets target
        target_time = speed_config.PERFORMANCE_SETTINGS['target_response_time']
        if processing_time > target_time and speed_config.PERFORMANCE_SETTINGS['warn_slow_responses']:
            print(f"⚠️ Slow response detected: {processing_time:.2f}s (target: {target_time}s)")

        # Return optimized JSON response
        return {
            "response": llama_response,
            "input_type": input_type,
            "success": True,
            "processing_time": f"{processing_time:.2f}s"
        }
    except Exception as e:
        print(f"❌ Error in response generation: {e}")
        return {
            "response": "I'm experiencing a temporary issue. Please try again.",
            "input_type": input_type,
            "success": False,
            "error": str(e)
        }

# Self-Improvement Endpoints

@app.get("/self_improvement/status")
async def get_improvement_status():
    """Get current status of the self-improvement system."""
    try:
        status = await jarvis_self_improvement.get_improvement_status()
        return {"success": True, "status": status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting status: {e}")

@app.post("/self_improvement/analyze")
async def analyze_system():
    """Perform comprehensive system analysis."""
    try:
        analysis = await jarvis_self_improvement.analyze_system()
        return {"success": True, "analysis": analysis}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing system: {e}")

@app.post("/self_improvement/implement")
async def implement_improvement(request: ImprovementRequest):
    """Implement a specific improvement suggestion."""
    try:
        if not request.suggestion_id:
            raise HTTPException(status_code=400, detail="suggestion_id is required")

        # This would typically retrieve the suggestion from storage
        # For now, we'll return a placeholder response
        result = {
            "success": True,
            "message": "Improvement implementation endpoint ready",
            "suggestion_id": request.suggestion_id,
            "auto_approve": request.auto_approve
        }
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error implementing improvement: {e}")

@app.get("/self_improvement/health")
async def get_system_health():
    """Get comprehensive system health report."""
    try:
        health = await jarvis_self_improvement.monitor.get_system_health()
        return {"success": True, "health": health}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting health: {e}")

@app.post("/self_improvement/backup")
async def create_improvement_backup():
    """Create a system backup."""
    try:
        backup_id = await jarvis_self_improvement.backup_manager.create_backup("manual", "Manual backup via API")
        return {"success": True, "backup_id": backup_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating backup: {e}")

@app.get("/self_improvement/backups")
async def list_improvement_backups():
    """List available backups."""
    try:
        backups = await jarvis_self_improvement.backup_manager.get_backup_list()
        return {"success": True, "backups": backups}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing backups: {e}")

@app.post("/self_improvement/report_error")
async def report_error_and_fix(request: ErrorReportRequest):
    """Report an error and attempt automatic fix."""
    try:
        result = await jarvis_self_improvement.process_error_report(
            error_description=request.error_description,
            expected_behavior=request.expected_behavior,
            steps_to_reproduce=request.steps_to_reproduce,
            severity=request.severity,
            auto_fix=request.auto_fix
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/progress")
async def get_current_progress():
    """Get current task progress for GUI updates."""
    global current_progress
    return {"success": True, "progress": current_progress}

# File Creation API Endpoints

class FileCreationRequest(BaseModel):
    filename: str
    template_type: str = 'basic_module'
    custom_content: Optional[str] = None
    description: Optional[str] = None
    imports: Optional[List[str]] = None
    auto_integrate: bool = True

class ProjectCreationRequest(BaseModel):
    project_name: str
    project_type: str = 'web_app'
    description: Optional[str] = None
    custom_structure: Optional[Dict] = None

@app.post("/create_file")
async def create_file(request: FileCreationRequest):
    """Create a new Python file with specified template."""
    try:
        result = file_creator.create_python_file(
            filename=request.filename,
            file_type=request.template_type,
            content=request.custom_content or ""
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating file: {e}")

@app.post("/create_project")
async def create_project(request: ProjectCreationRequest):
    """Create a complete project with multiple files."""
    try:
        result = await project_manager.create_project(
            project_name=request.project_name,
            project_type=request.project_type,
            description=request.description,
            custom_structure=request.custom_structure
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating project: {e}")

@app.get("/file_templates")
async def get_file_templates():
    """Get available file templates."""
    return {
        "success": True,
        "templates": list(file_creator.get_file_templates().keys()),
        "descriptions": {
            "basic_module": "Basic Python module with class structure",
            "flask_app": "Flask web application",
            "data_processor": "Data processing module with pandas/numpy",
            "api_client": "API client with requests and async support",
            "utility_functions": "Collection of utility functions",
            "class_module": "Abstract base class with concrete implementation",
            "async_module": "Asynchronous module with aiohttp",
            "gui_module": "Tkinter GUI application",
            "test_module": "Unit test module with unittest"
        }
    }

@app.get("/project_templates")
async def get_project_templates():
    """Get available project templates."""
    return {
        "success": True,
        "templates": list(project_manager.project_templates.keys()),
        "descriptions": {
            "web_app": "Flask web application with templates and static files",
            "data_analysis": "Data analysis project with pandas and visualization",
            "api_service": "REST API service with authentication and database",
            "gui_application": "Desktop GUI application with tkinter",
            "ml_project": "Machine learning project with training and evaluation",
            "utility_package": "Python package with utilities and tests"
        }
    }

# Web Intelligence & Learning Endpoints

@app.post("/web_intelligence/search")
async def web_search_and_learn(request: WebSearchRequest):
    """Search the web and optionally learn from results."""
    try:
        result = await jarvis_web_intelligence.search_and_learn(
            query=request.query,
            learn_from_results=request.learn_from_results
        )
        return {"success": True, "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error in web search: {e}")

@app.post("/web_intelligence/learn_from_url")
async def learn_from_url(request: URLLearningRequest):
    """Learn from a specific URL with progress tracking."""
    try:
        # Store progress updates for this request
        progress_updates = []

        async def progress_callback(percent, status, details):
            progress_updates.append({
                'percent': percent,
                'status': status,
                'details': details,
                'timestamp': datetime.now().isoformat()
            })

        result = await jarvis_web_intelligence.learn_from_url(
            url=request.url,
            progress_callback=progress_callback
        )

        # Include progress updates in response
        result['progress_updates'] = progress_updates
        return {"success": True, "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error learning from URL: {e}")

@app.post("/web_intelligence/youtube_learn")
async def learn_from_youtube(request: YouTubeLearnRequest):
    """Learn from a YouTube video."""
    try:
        result = await jarvis_web_intelligence.learn_from_youtube(
            video_url=request.video_url,
            extract_transcript=request.extract_transcript
        )
        return {"success": True, "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error learning from YouTube: {e}")

@app.post("/web_intelligence/knowledge_search")
async def search_knowledge(request: KnowledgeSearchRequest):
    """Search Jarvis's knowledge base."""
    try:
        result = await jarvis_web_intelligence.get_knowledge_for_query(
            query=request.query,
            max_results=request.max_results
        )
        return {"success": True, "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching knowledge: {e}")

@app.get("/web_intelligence/stats")
async def get_learning_stats():
    """Get comprehensive learning statistics."""
    try:
        stats = await jarvis_web_intelligence.get_learning_stats()
        return {"success": True, "stats": stats}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting learning stats: {e}")

@app.get("/web_intelligence/schedule")
async def get_learning_schedule():
    """Get the learning schedule for the next week."""
    try:
        schedule = await jarvis_web_intelligence.learning_scheduler.get_learning_schedule(days_ahead=7)
        return {"success": True, "schedule": schedule}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting learning schedule: {e}")

@app.post("/web_intelligence/summarize")
async def summarize_content(content: str, max_length: int = 500):
    """Summarize provided content."""
    try:
        result = await jarvis_web_intelligence.summarize_content(
            content=content,
            max_length=max_length
        )
        return {"success": True, "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error summarizing content: {e}")


# Security & Authentication Endpoints

@app.post("/security/login")
async def login(request: LoginRequest):
    """Authenticate user and return session token."""
    try:
        result = await jarvis_security_backup.authenticate_user(request.username, request.password)
        return {"success": result['success'], "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Login error: {e}")

@app.post("/security/logout")
async def logout(authorization: str = Header(None)):
    """Logout user and invalidate session."""
    try:
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization header")

        token = authorization.split(" ")[1]
        result = await jarvis_security_backup.logout_user(token)
        return {"success": result['success']}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Logout error: {e}")

@app.post("/security/create_user")
async def create_user(request: CreateUserRequest):
    """Create a new user account."""
    try:
        result = await jarvis_security_backup.create_user(
            username=request.username,
            password=request.password,
            email=request.email,
            role=request.role,
            full_name=request.full_name
        )
        return {"success": result['success'], "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"User creation error: {e}")

@app.get("/security/status")
async def get_security_status(authorization: str = Header(None)):
    """Get security system status."""
    try:
        token = None
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            # Check admin permission
            permission = await jarvis_security_backup.check_permission(token, 'system.admin')
            if not permission['authorized']:
                raise HTTPException(status_code=403, detail="Admin access required")

        status = jarvis_security_backup.get_system_status()
        return {"success": True, "status": status}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Status error: {e}")

# Backup & Recovery Endpoints

@app.post("/backup/create")
async def create_backup(request: BackupRequest, authorization: str = Header(None)):
    """Create a full system backup."""
    try:
        token = None
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]

        result = await jarvis_security_backup.create_full_backup(
            description=request.description,
            user_token=token
        )
        return {"success": result['success'], "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Backup creation error: {e}")

@app.get("/backup/list")
async def list_backups(limit: int = 50, authorization: str = Header(None)):
    """List available backups."""
    try:
        token = None
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]

        result = await jarvis_security_backup.list_backups(user_token=token, limit=limit)
        return {"success": result['success'], "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Backup listing error: {e}")

@app.post("/backup/restore")
async def restore_backup(request: RestoreRequest, authorization: str = Header(None)):
    """Restore a backup."""
    try:
        token = None
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]

        result = await jarvis_security_backup.restore_backup(
            backup_id=request.backup_id,
            restore_path=request.restore_path,
            user_token=token
        )
        return {"success": result['success'], "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Backup restore error: {e}")

@app.delete("/backup/{backup_id}")
async def delete_backup(backup_id: str, authorization: str = Header(None)):
    """Delete a backup."""
    try:
        token = None
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]

        result = await jarvis_security_backup.delete_backup(backup_id=backup_id, user_token=token)
        return {"success": result['success'], "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Backup deletion error: {e}")

@app.get("/backup/status")
async def get_backup_status():
    """Get backup system status."""
    try:
        status = jarvis_security_backup.backup.get_backup_status()
        return {"success": True, "status": status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Backup status error: {e}")

@app.post("/system/maintenance")
async def perform_maintenance(authorization: str = Header(None)):
    """Perform system maintenance."""
    try:
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Authorization required")

        token = authorization.split(" ")[1]
        result = await jarvis_security_backup.perform_system_maintenance(token)
        return {"success": result['success'], "data": result}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Maintenance error: {e}")


# Speed Testing Endpoint
@app.get("/speed_test")
async def speed_test():
    """Test Jarvis response speed."""
    start_time = time.time()

    # Test simple response
    test_response = llama_model.generate_response("Hello")

    processing_time = time.time() - start_time
    target_time = speed_config.PERFORMANCE_SETTINGS['target_response_time']

    return {
        "processing_time": f"{processing_time:.2f}s",
        "target_time": f"{target_time}s",
        "performance": "excellent" if processing_time < target_time else "needs_optimization",
        "cache_size": len(llama_model.response_cache),
        "test_response": test_response[:50] + "..." if len(test_response) > 50 else test_response
    }

# Self-Optimization Endpoint
@app.post("/self_optimize")
async def self_optimize(request: dict):
    """Allow Jarvis to optimize himself based on user request."""
    try:
        user_request = request.get('request', '')

        # Understand the optimization request
        optimization_request = await jarvis_self_optimizer.understand_optimization_request(user_request)

        if optimization_request['confidence'] > 0:
            # Analyze current performance
            await jarvis_self_optimizer.analyze_current_performance()

            # Implement optimizations
            results = await jarvis_self_optimizer.implement_optimizations(optimization_request)

            # Generate report
            report = jarvis_self_optimizer.generate_optimization_report(results)

            return {
                "success": True,
                "optimization_request": optimization_request,
                "results": results,
                "report": report
            }
        else:
            return {
                "success": False,
                "error": "Could not understand optimization request",
                "suggestion": "Try requests like 'make yourself faster' or 'improve performance'"
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    # Initialize security and backup systems
    asyncio.run(jarvis_security_backup.initialize())
    uvicorn.run(app="server:app", host=config.HOST, port=config.PORT, reload=False)

