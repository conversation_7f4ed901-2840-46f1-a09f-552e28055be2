#!/usr/bin/env python3
"""
📱 Local Network Setup for iPhone Access
No Tailscale required - works on same WiFi
"""

import socket
import subprocess
import platform
import webbrowser
import time

def get_all_local_ips():
    """Get all possible local IP addresses."""
    ips = []
    
    try:
        # Method 1: Get hostname IPs
        hostname = socket.gethostname()
        for info in socket.getaddrinfo(hostname, None):
            ip = info[4][0]
            if ip not in ips and not ip.startswith('127.') and ':' not in ip:
                ips.append(ip)
        
        # Method 2: Connect to external to find main IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            main_ip = s.getsockname()[0]
            if main_ip not in ips:
                ips.append(main_ip)
                
        # Method 3: Parse ipconfig output
        if platform.system() == "Windows":
            result = subprocess.run(['ipconfig'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if 'IPv4 Address' in line:
                    ip = line.split(':')[-1].strip()
                    if ip and not ip.startswith('127.') and ip not in ips:
                        ips.append(ip)
                        
    except Exception as e:
        print(f"Error getting IPs: {e}")
    
    return ips

def test_port_binding(ip, port):
    """Test if we can bind to IP and port."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind((ip, port))
            return True
    except:
        return False

def create_firewall_rules():
    """Create Windows Firewall rules."""
    ports = [8080, 3000, 8000, 5000]
    
    print("🛡️ WINDOWS FIREWALL SETUP")
    print("=" * 30)
    print("Run these commands as Administrator:")
    print()
    
    for port in ports:
        cmd = f'netsh advfirewall firewall add rule name="Jarvis Port {port}" dir=in action=allow protocol=TCP localport={port}'
        print(f"💻 {cmd}")
    
    print()
    print("🔧 OR use Windows GUI:")
    print("1. Windows Security → Firewall & network protection")
    print("2. Allow an app through firewall")
    print("3. Add Python.exe and check Private + Public")

def main():
    """Main setup function."""
    print("📱 LOCAL NETWORK SETUP FOR IPHONE")
    print("=" * 40)
    print("This works when iPhone and computer are on same WiFi")
    print()
    
    # Get all local IPs
    local_ips = get_all_local_ips()
    
    if not local_ips:
        print("❌ No local IP addresses found!")
        print("🔧 Check your network connection")
        return
    
    print("🌐 Available IP addresses:")
    working_ips = []
    
    for i, ip in enumerate(local_ips, 1):
        print(f"   {i}. {ip}")
        
        # Test if we can bind to this IP
        if test_port_binding(ip, 8080):
            working_ips.append(ip)
            print(f"      ✅ Can bind to port 8080")
        else:
            print(f"      ❌ Cannot bind to port 8080")
    
    if not working_ips:
        print("❌ No working IP addresses found!")
        create_firewall_rules()
        return
    
    print()
    print("📱 IPHONE ACCESS URLS:")
    print("=" * 25)
    
    for ip in working_ips:
        print(f"🔗 http://{ip}:8080")
    
    print()
    print("📋 IPHONE SETUP STEPS:")
    print("1. Connect iPhone to SAME WiFi as this computer")
    print("2. Open Safari on iPhone")
    print("3. Try each URL above until one works")
    print("4. Add working URL to Home Screen")
    print()
    
    # Test if Jarvis is running
    import requests
    jarvis_running = False
    
    for ip in working_ips:
        try:
            response = requests.get(f'http://{ip}:8080', timeout=3)
            if response.status_code == 200:
                print(f"✅ Jarvis Web App is accessible at: http://{ip}:8080")
                jarvis_running = True
                
                # Try to open in browser
                try:
                    webbrowser.open(f'http://{ip}:8080')
                except:
                    pass
                break
        except:
            continue
    
    if not jarvis_running:
        print("⚠️ Jarvis Web App is not responding")
        print("🔧 Make sure it's running: python jarvis_web_app.py")
        print()
    
    # Firewall check
    print("🛡️ FIREWALL CHECK:")
    if platform.system() == "Windows":
        try:
            result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'], 
                                  capture_output=True, text=True)
            if "ON" in result.stdout:
                print("🔴 Windows Firewall is ON - may block connections")
                print("🔧 Run firewall commands above as Administrator")
            else:
                print("🟢 Windows Firewall is OFF")
        except:
            print("⚠️ Cannot check firewall status")
    
    print()
    print("🎯 MOST LIKELY TO WORK:")
    if working_ips:
        best_ip = working_ips[0]
        print(f"📱 Try this URL first: http://{best_ip}:8080")
        print()
        print("💡 TIPS:")
        print("- Make sure iPhone is on same WiFi")
        print("- Try Safari private mode if it doesn't work")
        print("- Restart WiFi on iPhone if needed")
        print("- Check that computer isn't on guest network")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled")
    except Exception as e:
        print(f"\n❌ Error: {e}")
