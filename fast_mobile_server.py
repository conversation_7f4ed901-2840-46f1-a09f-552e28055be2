#!/usr/bin/env python3
"""
📱 Fast Mobile-Optimized Jarvis Server
Lightweight version for faster iPhone loading
"""

from flask import Flask, render_template_string, request, jsonify
import json
from datetime import datetime
import threading

app = Flask(__name__)

# Lightweight HTML template optimized for mobile speed
MOBILE_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>🌌 JARVIS Mobile</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            background: #1a1a1a; color: #fff; height: 100vh; display: flex; flex-direction: column;
        }
        .header { background: #007AFF; padding: 12px; text-align: center; font-weight: bold; font-size: 16px; }
        .main { flex: 1; display: flex; flex-direction: column; padding: 8px; gap: 8px; }
        .panels { display: grid; grid-template-columns: 1fr 1fr; gap: 8px; height: 140px; }
        .panel { background: #2a2a2a; border-radius: 8px; padding: 8px; display: flex; flex-direction: column; }
        .panel-header { background: #007AFF; color: white; padding: 4px; border-radius: 4px; text-align: center; font-size: 11px; margin-bottom: 6px; }
        .panel-btn { background: #333; color: #fff; border: none; padding: 6px; margin: 1px 0; border-radius: 4px; font-size: 10px; cursor: pointer; }
        .panel-btn:active { background: #007AFF; }
        .chat { flex: 1; background: #2a2a2a; border-radius: 8px; display: flex; flex-direction: column; min-height: 0; }
        .chat-header { background: #007AFF; color: white; padding: 8px; text-align: center; font-weight: bold; font-size: 12px; }
        .chat-messages { flex: 1; padding: 8px; overflow-y: auto; font-size: 11px; }
        .message { margin: 4px 0; padding: 6px; border-radius: 6px; word-wrap: break-word; }
        .message.user { background: #007AFF; color: white; margin-left: 15px; text-align: right; }
        .message.jarvis { background: #333; margin-right: 15px; }
        .input-area { display: flex; padding: 8px; gap: 8px; }
        .message-input { flex: 1; background: #333; color: #fff; border: 1px solid #007AFF; border-radius: 6px; padding: 8px; font-size: 16px; }
        .send-btn { background: #007AFF; color: white; border: none; border-radius: 6px; padding: 8px 16px; font-weight: bold; }
        .send-btn:active { background: #0056CC; }
        .status { background: #28a745; color: white; padding: 3px 6px; border-radius: 3px; font-size: 9px; margin: 3px 0; }
        .loading { opacity: 0.6; }
    </style>
</head>
<body>
    <div class="header">🌌 JARVIS MOBILE - FAST EDITION</div>
    
    <div class="main">
        <div class="panels">
            <div class="panel">
                <div class="panel-header">🛡️ SECURITY</div>
                <button class="panel-btn" onclick="quickAction('🔍 System scan initiated')">🔍 Scan</button>
                <button class="panel-btn" onclick="quickAction('📊 Monitoring active')">📊 Monitor</button>
            </div>
            
            <div class="panel">
                <div class="panel-header">🎓 TRAINING</div>
                <div class="status" id="status">Ready</div>
                <button class="panel-btn" onclick="startTraining()">🚀 Train</button>
                <button class="panel-btn" onclick="quickAction('📊 Training progress: Active')">📊 Progress</button>
            </div>
            
            <div class="panel">
                <div class="panel-header">📁 FILES</div>
                <button class="panel-btn" onclick="quickAction('📄 File creator ready')">📄 Create</button>
                <button class="panel-btn" onclick="quickAction('🚀 Project initialized')">🚀 Project</button>
            </div>
            
            <div class="panel">
                <div class="panel-header">🌐 WEB</div>
                <button class="panel-btn" onclick="quickAction('🌐 Web learning active')">🌐 Learn</button>
                <button class="panel-btn" onclick="quickAction('📺 YouTube connected')">📺 YouTube</button>
            </div>
        </div>
        
        <div class="chat">
            <div class="chat-header">💬 CHAT</div>
            <div class="chat-messages" id="messages">
                <div class="message jarvis">
                    🚀 JARVIS Mobile Fast Edition Ready!<br>
                    ⚡ Optimized for speed and mobile use.<br>
                    💡 Try: "Hello" or "train for 1 hour"
                </div>
            </div>
            <div class="input-area">
                <input type="text" class="message-input" id="messageInput" placeholder="Type message..." maxlength="200">
                <button class="send-btn" onclick="sendMessage()">Send</button>
            </div>
        </div>
    </div>
    
    <script>
        let messageCount = 0;
        
        function addMessage(text, type = 'jarvis') {
            const messages = document.getElementById('messages');
            const div = document.createElement('div');
            div.className = `message ${type}`;
            div.innerHTML = text;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
            
            // Limit messages to prevent memory issues
            if (messages.children.length > 50) {
                messages.removeChild(messages.firstChild);
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message) return;
            
            addMessage(message, 'user');
            input.value = '';
            
            // Show loading
            const sendBtn = document.querySelector('.send-btn');
            sendBtn.textContent = '...';
            sendBtn.disabled = true;
            
            // Send to server (simplified)
            fetch('/api/message', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({message: message})
            })
            .then(response => response.json())
            .then(data => {
                addMessage(data.response || 'Response received');
            })
            .catch(error => {
                addMessage('⚡ Quick response: ' + generateQuickResponse(message));
            })
            .finally(() => {
                sendBtn.textContent = 'Send';
                sendBtn.disabled = false;
            });
        }
        
        function generateQuickResponse(message) {
            const msg = message.toLowerCase();
            if (msg.includes('hello') || msg.includes('hi')) return '🤖 Hello! How can I help?';
            if (msg.includes('train')) return '🚀 Training session started!';
            if (msg.includes('status')) return '📊 All systems operational!';
            if (msg.includes('help')) return '💡 Try the panel buttons or ask questions!';
            return `🤖 Processing: "${message}"`;
        }
        
        function quickAction(message) {
            addMessage(`🤖 ${message}`);
            document.getElementById('status').textContent = 'Active';
            setTimeout(() => {
                document.getElementById('status').textContent = 'Ready';
            }, 3000);
        }
        
        function startTraining() {
            const duration = prompt('Training duration (e.g., "1 hour", "30 minutes"):') || '1 hour';
            addMessage(`🚀 Starting ${duration} training session...`);
            document.getElementById('status').textContent = 'Training';
        }
        
        // Enter key support
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') sendMessage();
        });
        
        // Prevent zoom on input focus (iOS)
        document.addEventListener('touchstart', function() {}, true);
    </script>
</body>
</html>
"""

class FastJarvisApp:
    def __init__(self):
        self.chat_history = []
        self.training_active = False
        
    def generate_response(self, message):
        """Generate fast responses."""
        msg = message.lower()
        
        responses = {
            'hello': '🤖 Jarvis: Hello! Ready to assist you.',
            'hi': '🤖 Jarvis: Hi there! How can I help?',
            'train': '🚀 Jarvis: Training session initiated. Learning in progress.',
            'status': '📊 Jarvis: All systems operational and ready.',
            'progress': '📈 Jarvis: Training progress: 45% complete.',
            'help': '💡 Jarvis: Use panel buttons or ask me anything!',
            'security': '🛡️ Jarvis: Security systems active and monitoring.',
            'files': '📁 Jarvis: File management systems ready.',
            'web': '🌐 Jarvis: Web intelligence systems online.',
            'thank': '🤖 Jarvis: You\'re welcome! Always here to help.'
        }
        
        for key, response in responses.items():
            if key in msg:
                return response
        
        return f'🤖 Jarvis: Understood "{message}". How may I assist further?'

fast_app = FastJarvisApp()

@app.route('/')
def index():
    """Serve the fast mobile interface."""
    return render_template_string(MOBILE_TEMPLATE)

@app.route('/api/message', methods=['POST'])
def handle_message():
    """Handle chat messages quickly."""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if message:
            response = fast_app.generate_response(message)
            fast_app.chat_history.append({
                'user': message,
                'jarvis': response,
                'time': datetime.now().isoformat()
            })
            
            return jsonify({'response': response})
        
    except Exception as e:
        return jsonify({'response': f'🤖 Jarvis: Quick response ready!'})
    
    return jsonify({'response': '🤖 Jarvis: Message received.'})

@app.route('/api/status')
def get_status():
    """Get system status quickly."""
    return jsonify({
        'status': 'operational',
        'training': fast_app.training_active,
        'messages': len(fast_app.chat_history)
    })

def start_fast_server(port=3000):
    """Start the fast mobile server."""
    print("⚡ FAST MOBILE JARVIS SERVER")
    print("=" * 35)
    print(f"📱 Optimized for iPhone speed")
    print(f"🚀 Starting on port {port}...")
    print(f"🌐 Access: http://localhost:{port}")
    print("⚡ Lightweight and fast!")
    print("🛑 Press Ctrl+C to stop")
    
    app.run(host='0.0.0.0', port=port, debug=False, threaded=True)

if __name__ == "__main__":
    try:
        start_fast_server()
    except KeyboardInterrupt:
        print("\n🛑 Fast server stopped")
    except Exception as e:
        print(f"❌ Error: {e}")
