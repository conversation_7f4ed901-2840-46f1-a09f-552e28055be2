"""
Test script for voice cloning functionality
This script demonstrates how the voice cloning system works
"""

import os
import json
from resemble_tts import initialize_tts, speak_text

def test_voice_cloning():
    """Test the voice cloning functionality."""
    print("🎤 Testing Voice Cloning System...")
    
    # Initialize TTS
    tts_engine = initialize_tts()
    
    # Test creating a voice clone (simulation mode)
    print("\n📁 Testing voice clone creation...")
    
    # Create a dummy audio file for testing
    test_audio_path = "test_audio.wav"
    
    # Create a simple test file (empty for demonstration)
    with open(test_audio_path, 'wb') as f:
        f.write(b'dummy audio data for testing')
    
    # Test voice cloning
    voice_name = "Test Jarvis Voice"
    voice_uuid = tts_engine.create_voice_clone(voice_name, test_audio_path)
    
    if voice_uuid:
        print(f"✅ Voice clone created successfully!")
        print(f"🎯 Voice Name: {voice_name}")
        print(f"🆔 Voice UUID: {voice_uuid}")
        
        # Set the new voice
        tts_engine.set_voice(voice_uuid)
        print(f"🔊 Voice set to: {voice_uuid}")
        
        # Test speaking with the new voice
        print("\n🎤 Testing speech with cloned voice...")
        test_text = "Hello sir, this is my new cloned voice. How do I sound?"
        tts_engine.speak(test_text)
        
        # Save voice configuration
        voice_config = {
            'name': voice_name,
            'source_file': test_audio_path,
            'voice_uuid': voice_uuid,
            'quality': 'high',
            'created_at': '2024-01-01T00:00:00',
            'status': 'active',
            'provider': 'resemble_ai'
        }
        
        # Create voices directory
        voices_dir = "jarvis_voices"
        if not os.path.exists(voices_dir):
            os.makedirs(voices_dir)
        
        # Save voice config
        voice_file = f"{voices_dir}/{voice_name.lower().replace(' ', '_')}_voice.json"
        with open(voice_file, 'w') as f:
            json.dump(voice_config, f, indent=2)
        
        print(f"💾 Voice configuration saved to: {voice_file}")
        
        # Show voice config
        print("\n📋 Voice Configuration:")
        print(json.dumps(voice_config, indent=2))
        
    else:
        print("❌ Voice clone creation failed!")
    
    # Clean up test file
    try:
        os.remove(test_audio_path)
        print(f"\n🧹 Cleaned up test file: {test_audio_path}")
    except:
        pass
    
    print("\n✅ Voice cloning test complete!")

def show_voice_cloning_instructions():
    """Show instructions for voice cloning."""
    print("""
🎤 VOICE CLONING INSTRUCTIONS
=============================

METHOD 1 - Local Audio File:
1. 📁 Click "Browse Audio File" in the Jarvis GUI
2. 🎵 Select a high-quality audio file (.wav, .mp3, .m4a)
3. 🎯 Enter a name for the voice (e.g., "Iron Man Jarvis")
4. ⚙️ Choose quality setting (High recommended)
5. 🚀 Click "Start Voice Cloning"

METHOD 2 - YouTube URL:
1. 🔗 Paste a YouTube URL with clear speech
2. 🎯 Enter a name for the voice
3. ⚙️ Choose quality setting
4. 🚀 Click "Start Voice Cloning"

REQUIREMENTS:
• 🎵 Clear, high-quality speech (minimum 30 seconds)
• 🔇 Minimal background noise
• 🗣️ English language preferred
• 📁 Supported formats: .wav, .mp3, .m4a, .flac, .ogg

FEATURES:
• 🤖 Resemble AI integration for professional voice cloning
• 🔄 Fallback to local processing if API unavailable
• 💾 Automatic voice profile saving
• 🔊 Instant voice switching
• 🎭 Iron Man Jarvis personality maintained

TIPS:
• 🎯 Use clear, consistent speech samples
• 🎤 Record in quiet environment for best results
• ⏱️ Longer samples (1-2 minutes) produce better clones
• 🔊 Test different quality settings for optimal results
""")

if __name__ == "__main__":
    print("🎤 Voice Cloning Test & Demo")
    print("=" * 50)
    
    # Show instructions
    show_voice_cloning_instructions()
    
    # Run test
    test_voice_cloning()
